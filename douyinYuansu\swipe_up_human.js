/**
 * 模拟真人上滑操作模块
 * 提供多种真实的上滑手势，模拟人类的滑动行为
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 基础上滑操作 - 模拟最常见的上滑手势
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function basicSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行基础上滑操作..."); // 输出操作日志

    try {
        // 获取屏幕尺寸
        var width = device.width; // 屏幕宽度
        var height = device.height; // 屏幕高度

        // 默认配置参数
        var config = {
            startXRatio: 0.5,      // 起始X位置比例（屏幕中央）
            startYRatio: 0.75,     // 起始Y位置比例（屏幕下方3/4处）
            endYRatio: 0.25,       // 结束Y位置比例（屏幕上方1/4处）
            duration: 350,         // 滑动持续时间（毫秒）
            randomOffset: 15       // 随机偏移范围（像素）
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        // 计算滑动坐标
        var startX = width * config.startXRatio + random(-config.randomOffset, config.randomOffset); // 起始X坐标
        var startY = height * config.startYRatio + random(-config.randomOffset, config.randomOffset); // 起始Y坐标
        var endX = startX + random(-8, 8); // 结束X坐标（轻微水平偏移）
        var endY = height * config.endYRatio + random(-config.randomOffset, config.randomOffset); // 结束Y坐标

        // 添加随机的滑动时间变化
        var actualDuration = config.duration + random(-50, 100); // 实际滑动时间

        console.log("基础上滑坐标: (" + startX.toFixed(1) + ", " + startY.toFixed(1) + ") -> (" + endX.toFixed(1) + ", " + endY.toFixed(1) + ")"); // 输出坐标信息
        console.log("滑动时间: " + actualDuration + "ms"); // 输出滑动时间

        // 执行滑动操作
        swipe(startX, startY, endX, endY, actualDuration); // 执行上滑

        return true; // 返回成功
    } catch (error) {
        console.error("基础上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 快速上滑操作 - 模拟快速浏览时的上滑手势
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function fastSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行快速上滑操作..."); // 输出操作日志

    try {
        var width = device.width; // 屏幕宽度
        var height = device.height; // 屏幕高度

        // 快速滑动配置
        var config = {
            startXRatio: 0.5,      // 起始X位置比例
            startYRatio: 0.7,      // 起始Y位置比例（稍微靠上）
            endYRatio: 0.15,       // 结束Y位置比例（更靠上）
            duration: 200,         // 更短的滑动时间
            randomOffset: 20       // 更大的随机偏移
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        // 计算坐标
        var startX = width * config.startXRatio + random(-config.randomOffset, config.randomOffset); // 起始X坐标
        var startY = height * config.startYRatio + random(-10, 10); // 起始Y坐标
        var endX = startX + random(-12, 12); // 结束X坐标
        var endY = height * config.endYRatio + random(-config.randomOffset, config.randomOffset); // 结束Y坐标

        var actualDuration = config.duration + random(-30, 50); // 实际滑动时间

        console.log("快速上滑坐标: (" + startX.toFixed(1) + ", " + startY.toFixed(1) + ") -> (" + endX.toFixed(1) + ", " + endY.toFixed(1) + ")"); // 输出坐标信息
        console.log("滑动时间: " + actualDuration + "ms"); // 输出滑动时间

        swipe(startX, startY, endX, endY, actualDuration); // 执行快速上滑

        return true; // 返回成功
    } catch (error) {
        console.error("快速上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 慢速上滑操作 - 模拟仔细观看时的慢速上滑手势
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function slowSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行慢速上滑操作..."); // 输出操作日志

    try {
        var width = device.width; // 屏幕宽度
        var height = device.height; // 屏幕高度

        // 慢速滑动配置
        var config = {
            startXRatio: 0.5,      // 起始X位置比例
            startYRatio: 0.8,      // 起始Y位置比例（更靠下）
            endYRatio: 0.3,        // 结束Y位置比例（不太靠上）
            duration: 600,         // 更长的滑动时间
            randomOffset: 12       // 较小的随机偏移
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        // 计算坐标
        var startX = width * config.startXRatio + random(-config.randomOffset, config.randomOffset); // 起始X坐标
        var startY = height * config.startYRatio + random(-8, 8); // 起始Y坐标
        var endX = startX + random(-6, 6); // 结束X坐标
        var endY = height * config.endYRatio + random(-config.randomOffset, config.randomOffset); // 结束Y坐标

        var actualDuration = config.duration + random(-100, 150); // 实际滑动时间

        console.log("慢速上滑坐标: (" + startX.toFixed(1) + ", " + startY.toFixed(1) + ") -> (" + endX.toFixed(1) + ", " + endY.toFixed(1) + ")"); // 输出坐标信息
        console.log("滑动时间: " + actualDuration + "ms"); // 输出滑动时间

        swipe(startX, startY, endX, endY, actualDuration); // 执行慢速上滑

        return true; // 返回成功
    } catch (error) {
        console.error("慢速上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 曲线上滑操作 - 模拟带有轻微弧度的上滑手势
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function curveSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行曲线上滑操作..."); // 输出操作日志

    try {
        var width = device.width; // 屏幕宽度
        var height = device.height; // 屏幕高度

        // 曲线滑动配置
        var config = {
            startXRatio: 0.5,      // 起始X位置比例
            startYRatio: 0.75,     // 起始Y位置比例
            endYRatio: 0.25,       // 结束Y位置比例
            duration: 400,         // 滑动时间
            curveIntensity: 25     // 曲线强度（像素）
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        // 计算起始和结束坐标
        var startX = width * config.startXRatio + random(-15, 15); // 起始X坐标
        var startY = height * config.startYRatio + random(-10, 10); // 起始Y坐标
        var endY = height * config.endYRatio + random(-15, 15); // 结束Y坐标

        // 计算中间点，形成轻微弧度
        var midY = (startY + endY) / 2; // 中间Y坐标
        var curveDirection = random(0, 1) === 0 ? -1 : 1; // 随机选择曲线方向
        var midX = startX + (config.curveIntensity * curveDirection); // 中间X坐标（带弧度）
        var endX = startX + random(-10, 10); // 结束X坐标

        var actualDuration = config.duration + random(-50, 80); // 实际滑动时间

        console.log("曲线上滑路径: (" + startX.toFixed(1) + ", " + startY.toFixed(1) + ") -> (" + midX.toFixed(1) + ", " + midY.toFixed(1) + ") -> (" + endX.toFixed(1) + ", " + endY.toFixed(1) + ")"); // 输出路径信息
        console.log("滑动时间: " + actualDuration + "ms"); // 输出滑动时间

        // 分两段执行曲线滑动
        var halfDuration = Math.floor(actualDuration / 2); // 每段时间
        swipe(startX, startY, midX, midY, halfDuration); // 第一段滑动
        sleep(10); // 短暂停顿
        swipe(midX, midY, endX, endY, halfDuration); // 第二段滑动

        return true; // 返回成功
    } catch (error) {
        console.error("曲线上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 多段式上滑操作 - 模拟分段式的上滑手势（更像真人）
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function multiStageSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行多段式上滑操作..."); // 输出操作日志

    try {
        var width = device.width; // 屏幕宽度
        var height = device.height; // 屏幕高度

        // 多段滑动配置
        var config = {
            startXRatio: 0.5,      // 起始X位置比例
            startYRatio: 0.8,      // 起始Y位置比例
            endYRatio: 0.2,        // 结束Y位置比例
            stages: 3,             // 分段数量
            totalDuration: 500,    // 总滑动时间
            pauseBetweenStages: 15 // 段间停顿时间
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        // 计算起始坐标
        var startX = width * config.startXRatio + random(-12, 12); // 起始X坐标
        var startY = height * config.startYRatio + random(-8, 8); // 起始Y坐标
        var endY = height * config.endYRatio + random(-12, 12); // 结束Y坐标

        // 计算每段的距离和时间
        var totalDistance = startY - endY; // 总滑动距离
        var stageDistance = totalDistance / config.stages; // 每段距离
        var stageDuration = Math.floor(config.totalDuration / config.stages); // 每段时间

        console.log("多段式上滑: " + config.stages + "段，总距离: " + totalDistance.toFixed(1) + "px"); // 输出滑动信息

        var currentX = startX; // 当前X坐标
        var currentY = startY; // 当前Y坐标

        // 执行分段滑动
        for (var i = 0; i < config.stages; i++) {
            var nextY = currentY - stageDistance + random(-5, 5); // 下一段的Y坐标
            var nextX = currentX + random(-8, 8); // 下一段的X坐标（轻微偏移）
            var actualStageDuration = stageDuration + random(-20, 30); // 实际段时间

            console.log("第" + (i + 1) + "段: (" + currentX.toFixed(1) + ", " + currentY.toFixed(1) + ") -> (" + nextX.toFixed(1) + ", " + nextY.toFixed(1) + ")"); // 输出段信息

            swipe(currentX, currentY, nextX, nextY, actualStageDuration); // 执行段滑动

            // 段间停顿（除了最后一段）
            if (i < config.stages - 1) {
                sleep(config.pauseBetweenStages + random(-5, 10)); // 段间停顿
            }

            currentX = nextX; // 更新当前X坐标
            currentY = nextY; // 更新当前Y坐标
        }

        return true; // 返回成功
    } catch (error) {
        console.error("多段式上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 智能上滑操作 - 根据情况自动选择最合适的上滑方式
 * @param {Object} options 配置选项
 * @returns {boolean} 操作是否成功
 */
function smartSwipeUp(options) {
    // 处理默认参数
    if (!options) options = {};

    console.log("执行智能上滑操作..."); // 输出操作日志

    try {
        // 默认配置
        var config = {
            mode: "auto",          // 模式：auto（自动）、basic、fast、slow、curve、multi
            adaptToTime: true      // 是否根据时间调整滑动方式
        };

        // 合并用户配置
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                config[key] = options[key];
            }
        }

        var swipeMode = config.mode; // 滑动模式

        // 自动模式：根据时间和随机性选择滑动方式
        if (swipeMode === "auto") {
            var currentHour = new Date().getHours(); // 当前小时
            var randomChoice = random(1, 100); // 随机数

            // 根据时间段和随机性选择滑动方式
            if (currentHour >= 9 && currentHour <= 17) {
                // 工作时间：更多快速滑动
                if (randomChoice <= 40) {
                    swipeMode = "fast"; // 40%概率快速滑动
                } else if (randomChoice <= 70) {
                    swipeMode = "basic"; // 30%概率基础滑动
                } else if (randomChoice <= 85) {
                    swipeMode = "curve"; // 15%概率曲线滑动
                } else {
                    swipeMode = "multi"; // 15%概率多段滑动
                }
            } else {
                // 非工作时间：更多慢速和复杂滑动
                if (randomChoice <= 25) {
                    swipeMode = "slow"; // 25%概率慢速滑动
                } else if (randomChoice <= 50) {
                    swipeMode = "basic"; // 25%概率基础滑动
                } else if (randomChoice <= 75) {
                    swipeMode = "curve"; // 25%概率曲线滑动
                } else {
                    swipeMode = "multi"; // 25%概率多段滑动
                }
            }
        }

        console.log("选择的滑动模式: " + swipeMode); // 输出选择的模式

        // 根据选择的模式执行相应的滑动操作
        var result = false; // 操作结果
        switch (swipeMode) {
            case "basic":
                result = basicSwipeUp(options); // 执行基础滑动
                break;
            case "fast":
                result = fastSwipeUp(options); // 执行快速滑动
                break;
            case "slow":
                result = slowSwipeUp(options); // 执行慢速滑动
                break;
            case "curve":
                result = curveSwipeUp(options); // 执行曲线滑动
                break;
            case "multi":
                result = multiStageSwipeUp(options); // 执行多段滑动
                break;
            default:
                result = basicSwipeUp(options); // 默认执行基础滑动
                break;
        }

        return result; // 返回操作结果
    } catch (error) {
        console.error("智能上滑操作失败: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 随机数生成函数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}
// smartSwipeUp()
// 导出模块函数
module.exports = {
    basicSwipeUp: basicSwipeUp,           // 基础上滑
    fastSwipeUp: fastSwipeUp,             // 快速上滑
    slowSwipeUp: slowSwipeUp,             // 慢速上滑
    curveSwipeUp: curveSwipeUp,           // 曲线上滑
    multiStageSwipeUp: multiStageSwipeUp, // 多段上滑
    smartSwipeUp: smartSwipeUp            // 智能上滑（推荐使用）
};

/**
 * 使用示例：
 *
 * // 引入模块
 * var swipeModule = require("./douyinYuansu/swipe_up_human.js");
 *
 * // 使用智能上滑（推荐）
 * swipeModule.smartSwipeUp();
 *
 * // 使用特定的滑动方式
 * swipeModule.basicSwipeUp();
 * swipeModule.fastSwipeUp();
 * swipeModule.slowSwipeUp();
 * swipeModule.curveSwipeUp();
 * swipeModule.multiStageSwipeUp();
 *
 * // 使用自定义配置
 * swipeModule.smartSwipeUp({
 *     mode: "curve",
 *     startYRatio: 0.8,
 *     endYRatio: 0.2
 * });
 */
