/**
 * 点击抖音关闭按钮模块
 * 根据控件属性定位并模拟真人点击关闭按钮
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找关闭按钮的主函数
 * @returns {UiObject|null} 找到的关闭按钮控件，未找到返回null
 */
function findCloseButton() {
    console.log("开始查找关闭按钮..."); // 输出开始查找日志
    
    try {
        // 方法1: 通过完整的资源ID查找（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var buttonByFullId = id("com.ss.android.ugc.aweme:id/back_btn").visibleToUser().findOne(3000); // 使用完整的资源ID，确保可见，等待3秒
        if (buttonByFullId) {
            console.log("✓ 通过完整资源ID找到关闭按钮"); // 输出找到按钮的日志
            return buttonByFullId; // 返回找到的按钮
        }
        
        // 方法2: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var buttonByShortId = id("back_btn").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (buttonByShortId) {
            console.log("✓ 通过简短ID找到关闭按钮"); // 输出找到按钮的日志
            return buttonByShortId; // 返回找到的按钮
        }
        
        // 方法3: 通过desc内容查找
        console.log("方法3: 尝试通过desc内容查找..."); // 输出当前尝试的方法
        var buttonByDesc = desc("关闭").visibleToUser().findOne(2000); // 通过desc查找
        if (buttonByDesc) {
            console.log("✓ 通过desc内容找到关闭按钮"); // 输出找到按钮的日志
            return buttonByDesc; // 返回找到的按钮
        }
        
        // 方法4: 通过desc关键词查找
        console.log("方法4: 尝试通过desc关键词查找..."); // 输出当前尝试的方法
        var buttonByDescContains = descContains("关闭").visibleToUser().findOne(2000); // 查找desc包含"关闭"的控件
        if (buttonByDescContains) {
            console.log("✓ 通过desc关键词找到关闭按钮，desc: " + buttonByDescContains.desc()); // 输出找到按钮的日志
            return buttonByDescContains; // 返回找到的按钮
        }
        
        // 方法5: 通过属性组合查找
        console.log("方法5: 尝试通过属性组合查找..."); // 输出当前尝试的方法
        var buttonByProps = enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒
            
        if (buttonByProps) {
            // 进一步验证desc内容或ID
            var propsDesc = buttonByProps.desc(); // 获取控件描述
            var propsId = buttonByProps.id(); // 获取控件ID
            if ((propsDesc && propsDesc.includes("关闭")) || 
                (propsId && propsId.includes("back_btn"))) {
                console.log("✓ 通过属性组合找到关闭按钮，desc: " + propsDesc + ", id: " + propsId); // 输出找到按钮的日志
                return buttonByProps; // 返回找到的按钮
            }
        }
        
        // 方法6: 通过多重属性精确匹配查找
        console.log("方法6: 尝试通过多重属性精确匹配查找..."); // 输出当前尝试的方法
        var buttonByMultiProps = enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒
            
        if (buttonByMultiProps) {
            // 进一步验证是否为关闭按钮
            var multiPropsDesc = buttonByMultiProps.desc(); // 获取控件描述
            var multiPropsId = buttonByMultiProps.id(); // 获取控件ID
            if ((multiPropsDesc && multiPropsDesc === "关闭") || 
                (multiPropsId && multiPropsId.includes("back_btn"))) {
                console.log("✓ 通过多重属性精确匹配找到关闭按钮，desc: " + multiPropsDesc); // 输出找到按钮的日志
                return buttonByMultiProps; // 返回找到的按钮
            }
        }
        
        // 方法7: 遍历所有可见控件查找
        console.log("方法7: 遍历所有可见控件查找..."); // 输出当前尝试的方法
        var allVisibleViews = enabled(true).focusable(true).visibleToUser().find(); // 查找所有可见的启用且可获得焦点的控件
        console.log("找到 " + allVisibleViews.length + " 个符合条件的可见控件"); // 输出找到的控件数量
        
        for (var i = 0; i < allVisibleViews.length; i++) {
            var element = allVisibleViews[i]; // 遍历每个控件
            var elementId = element.id(); // 获取控件ID
            var elementDesc = element.desc(); // 获取控件描述
            var elementPackage = element.packageName(); // 获取包名
            
            // 检查是否符合关闭按钮的特征
            if (elementPackage && elementPackage.includes("aweme") && 
                ((elementId && elementId.includes("back_btn")) || 
                 (elementDesc && elementDesc.includes("关闭")))) {
                console.log("✓ 通过遍历找到关闭按钮 - ID: " + elementId + ", desc: " + elementDesc); // 输出找到按钮的日志
                return element; // 返回找到的按钮
            }
        }
        
        console.log("✗ 所有方法都未找到关闭按钮"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到
        
    } catch (error) {
        console.error("✗ 查找关闭按钮时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} button - 要点击的按钮控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(button) {
    console.log("开始模拟真人点击关闭按钮操作..."); // 输出开始点击日志
    
    try {
        // 获取按钮的位置信息
        var bounds = button.bounds(); // 获取按钮边界
        if (!bounds) {
            console.error("✗ 无法获取按钮位置信息"); // 输出错误信息
            return false; // 返回失败
        }
        
        console.log("按钮位置: " + bounds.toString()); // 输出按钮位置
        
        // 计算按钮中心点坐标
        var centerX = bounds.centerX(); // 按钮中心X坐标
        var centerY = bounds.centerY(); // 按钮中心Y坐标
        
        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-6, 6); // X轴随机偏移-6到6像素
        var randomOffsetY = random(-4, 4); // Y轴随机偏移-4到4像素
        
        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标
        
        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标
        
        // 模拟真人的点击前停顿
        var preClickDelay = random(120, 350); // 点击前随机延迟120-350毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟
        
        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = press(clickX, clickY, random(60, 180)); // 执行坐标点击，随机按压时长
        
        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = button.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }
        
        // 模拟真人的点击后停顿
        var postClickDelay = random(250, 500); // 点击后随机延迟250-500毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟
        
        console.log("✓ 模拟真人点击关闭按钮操作完成"); // 输出完成日志
        return true; // 返回成功
        
    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击关闭按钮
 * @returns {boolean} 操作是否成功
 */
function clickCloseButton() {
    console.log("=== 开始执行点击关闭按钮操作 ==="); // 输出开始操作日志
    
    // 检查是否在抖音应用中
    // if (!currentPackage().includes("aweme")) {
    //     console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
    //     launchApp("抖音"); // 启动抖音应用
    //     sleep(3000); // 等待3秒让应用加载
    //     waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    // }
    
    // 查找关闭按钮
    var closeButton = findCloseButton(); // 调用查找函数
    
    if (!closeButton) {
        console.error("✗ 未找到关闭按钮，操作失败"); // 输出失败信息
        toast("未找到关闭按钮"); // 显示失败提示
        return false; // 返回失败
    }
    
    console.log("✓ 成功找到关闭按钮"); // 输出成功找到日志
    toast("找到关闭按钮，准备点击"); // 显示找到提示
    
    // 执行模拟真人点击
    var clickResult = humanLikeClick(closeButton); // 调用点击函数
    
    if (clickResult) {
        console.log("✓ 关闭按钮点击成功"); // 输出成功日志
        toast("✓ 关闭按钮点击成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 关闭按钮点击失败"); // 输出失败日志
        toast("✗ 关闭按钮点击失败"); // 显示失败提示
        return false; // 返回失败
    }
}

/**
 * 测试函数：测试查找和点击功能
 */
function testClickCloseButton() {
    console.log("=== 开始测试点击关闭按钮功能 ==="); // 输出测试开始信息
    
    // 测试查找功能
    console.log("\n📝 测试1: 查找关闭按钮"); // 输出测试类型
    var button = findCloseButton(); // 查找按钮
    if (button) {
        console.log("✓ 查找测试成功"); // 输出测试成功
        console.log("按钮信息: ID=" + button.id() + ", desc=" + button.desc()); // 输出按钮信息
        console.log("按钮详细属性:"); // 输出详细属性标题
        console.log("  - className: " + button.className()); // 输出类名
        console.log("  - editable: " + button.editable); // 输出可编辑状态
        console.log("  - enabled: " + button.enabled()); // 输出启用状态
        console.log("  - focusable: " + button.focusable()); // 输出可获得焦点状态
        console.log("  - longClickable: " + button.longClickable()); // 输出可长按状态
        console.log("  - visibleToUser: " + button.visibleToUser()); // 输出可见性状态
        console.log("  - bounds: " + button.bounds()); // 输出边界信息
    } else {
        console.log("✗ 查找测试失败"); // 输出测试失败
    }
    
    // 测试完整流程
    console.log("\n📝 测试2: 完整点击流程"); // 输出测试类型
    var result = clickCloseButton(); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果
    
    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

// 导出模块，供其他脚本调用
module.exports = {
    findCloseButton: findCloseButton, // 导出查找关闭按钮函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickCloseButton: clickCloseButton, // 导出主函数
    testClickCloseButton: testClickCloseButton // 导出测试函数
};

// 如果直接运行此脚本，则执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("直接运行click_close_button.js，开始执行测试..."); // 输出直接运行提示
    testClickCloseButton(); // 执行测试函数
}
