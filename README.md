# 抖音自动评论系统

一个基于Auto.js的抖音自动评论系统，支持完整的评论流程：查找评论按钮、点击输入框、输入内容、发送评论。

## 📋 功能特性

- ✅ **智能控件查找**: 基于多种属性组合查找UI控件
- ✅ **真人模拟操作**: 随机延迟和偏移，模拟真实用户行为
- ✅ **多重备选方案**: 每个功能都有多种查找方法作为备选
- ✅ **完整错误处理**: 详细的日志记录和异常处理
- ✅ **可见性验证**: 所有查找都使用 `.visibleToUser()` 确保控件可见
- ✅ **模块化设计**: 每个功能独立模块，便于维护和扩展
- ✅ **广告智能检测**: 基于text和desc属性自动识别广告内容
- ✅ **广告自动跳过**: 检测到广告时自动跳过，提升观看体验

## 📁 文件结构

### 核心模块
- `click_comment_button.js` - 评论按钮查找和点击
- `click_comment_input.js` - 留言输入框查找和点击
- `click_send_button.js` - 发送按钮查找和点击
- `click_close_button.js` - 关闭按钮查找和点击
- `click_share_button.js` - 分享按钮查找和点击
- `click_favorite_button.js` - 收藏按钮查找和点击
- `click_follow_button.js` - 关注按钮查找和点击
- `complete_comment_flow.js` - 完整评论流程整合

### 广告检测模块
- `ad_detection.js` - 广告检测核心模块
- `ad_patterns.js` - 广告模式配置文件
- `test_ad_detection.js` - 广告检测测试脚本
- `ad_detection_example.js` - 广告检测使用示例

### 测试脚本
- `test_comment_button_search.js` - 测试评论按钮查找
- `test_comment_input_search.js` - 测试留言输入框查找
- `test_send_button_search.js` - 测试发送按钮查找
- `test_close_button_search.js` - 测试关闭按钮查找
- `test_share_button_search.js` - 测试分享按钮查找
- `test_favorite_button_search.js` - 测试收藏按钮查找
- `test_follow_button_search.js` - 测试关注按钮查找
- `test_editable_fix.js` - 验证属性访问修正
- `comment_workflow_example.js` - 评论流程示例
- `close_button_example.js` - 关闭按钮使用示例
- `share_button_example.js` - 分享按钮使用示例
- `favorite_button_example.js` - 收藏按钮使用示例
- `smart_favorite_example.js` - 智能收藏示例（仅未选中时点击）
- `smart_follow_example.js` - 智能关注示例（优先desc查找）

### 文档
- `comment_button_search_strategy.md` - 评论按钮查找策略
- `comment_input_search_strategy.md` - 输入框和发送按钮查找策略
- `autojs_property_reference.md` - Auto.js属性访问参考
- `update_summary.md` - 更新总结

## 🚀 快速开始

### 1. 环境准备
- 安装Auto.js应用
- 开启无障碍服务权限
- 确保抖音应用已安装

### 2. 基本使用

#### 单独使用各模块
```javascript
// 使用评论按钮模块
var commentButtonModule = require('./click_comment_button.js');
var button = commentButtonModule.findCommentButton();
if (button) {
    commentButtonModule.humanLikeClick(button);
}

// 使用输入框模块
var inputModule = require('./click_comment_input.js');
var input = inputModule.findCommentInput();
if (input) {
    inputModule.humanLikeClick(input);
}

// 使用发送按钮模块
var sendModule = require('./click_send_button.js');
var sendBtn = sendModule.findSendButton();
if (sendBtn) {
    sendModule.humanLikeClick(sendBtn);
}

// 使用关闭按钮模块
var closeModule = require('./click_close_button.js');
var closeBtn = closeModule.findCloseButton();
if (closeBtn) {
    closeModule.humanLikeClick(closeBtn);
}

// 使用分享按钮模块
var shareModule = require('./click_share_button.js');
var shareBtn = shareModule.findShareButton();
if (shareBtn) {
    shareModule.humanLikeClick(shareBtn);
}

// 使用收藏按钮模块（智能收藏）
var favoriteModule = require('./click_favorite_button.js');

// 方法1: 智能收藏（推荐 - 仅未选中时点击）
favoriteModule.smartFavoriteClick();

// 方法2: 传统方式
var favoriteBtn = favoriteModule.findFavoriteButton();
if (favoriteBtn) {
    favoriteModule.humanLikeClick(favoriteBtn);
}

// 使用关注按钮模块（智能关注）
var followModule = require('./click_follow_button.js');

// 方法1: 智能关注（推荐 - 优先desc查找）
followModule.smartFollowClick();

// 方法2: 传统方式
var followBtn = followModule.findFollowButton();
if (followBtn) {
    followModule.humanLikeClick(followBtn);
}

// 使用广告检测模块
var adDetection = require('./ad_detection.js');

// 方法1: 检测当前屏幕是否包含广告
var hasAd = adDetection.hasAdOnScreen();
if (hasAd) {
    console.log("检测到广告，执行跳过操作");
}

// 方法2: 根据文本和描述检测广告
var isAd = adDetection.isAdByTextAndDesc("上海维继看视频", "上海维继看视频");
console.log("广告检测结果: " + (isAd ? "是广告" : "不是广告"));

// 方法3: 查找所有广告元素
var adElements = adDetection.findAllAdElements();
console.log("发现 " + adElements.length + " 个广告元素");
```

#### 使用完整流程
```javascript
var completeFlow = require('./complete_comment_flow.js');

// 发送评论
var success = completeFlow.executeCompleteCommentFlow("很棒的视频！");
if (success) {
    console.log("评论发送成功");
}
```

### 3. 运行测试

#### 测试单个模块
```bash
# 测试评论按钮
node click_comment_button.js

# 测试输入框
node click_comment_input.js

# 测试发送按钮
node click_send_button.js

# 测试关闭按钮
node click_close_button.js

# 测试分享按钮
node click_share_button.js

# 测试收藏按钮
node click_favorite_button.js

# 测试关注按钮
node click_follow_button.js
```

#### 测试完整流程
```bash
# 运行完整流程测试
node complete_comment_flow.js
```

#### 测试广告检测
```bash
# 测试广告检测功能
node test_ad_detection.js

# 运行广告检测使用示例
node ad_detection_example.js
```

## 🎯 核心原理

### 控件查找策略

#### 1. 评论按钮 (ID: d5d)
```javascript
// 优先级1: 资源ID查找
id("com.ss.android.ugc.aweme:id/d5d").visibleToUser().findOne(3000)

// 优先级2: 属性组合查找
className("android.widget.ImageView")
    .depth(3)
    .enabled(true)
    .clickable(true)
    .visibleToUser()
    .findOne(2000)

// 优先级3: desc模式匹配
descContains("评论").visibleToUser().findOne(2000)
```

#### 2. 留言输入框 (ID: dyd)
```javascript
// 优先级1: 资源ID查找
id("com.ss.android.ugc.aweme:id/dyd").visibleToUser().findOne(3000)

// 优先级2: text内容查找
textContains("善恶终有报").visibleToUser().findOne(2000)

// 优先级3: 属性组合查找
editable(true)
    .enabled(true)
    .focusable(true)
    .visibleToUser()
    .findOne(2000)
```

#### 3. 发送按钮 (ID: d2h)
```javascript
// 优先级1: 资源ID查找
id("com.ss.android.ugc.aweme:id/d2h").visibleToUser().findOne(3000)

// 优先级2: text内容查找
text("发送").visibleToUser().findOne(2000)

// 优先级3: 属性组合查找
enabled(true)
    .visibleToUser()
    .packageName("com.ss.android.ugc.aweme")
    .findOne(2000)
```

#### 4. 关闭按钮 (ID: back_btn)
```javascript
// 优先级1: 资源ID查找
id("com.ss.android.ugc.aweme:id/back_btn").visibleToUser().findOne(3000)

// 优先级2: desc内容查找
desc("关闭").visibleToUser().findOne(2000)

// 优先级3: 属性组合查找
enabled(true)
    .focusable(true)
    .visibleToUser()
    .packageName("com.ss.android.ugc.aweme")
    .findOne(2000)
```

#### 5. 分享按钮 (ID: w7n)
```javascript
// 优先级1: 资源ID查找
id("com.ss.android.ugc.aweme:id/w7n").visibleToUser().findOne(3000)

// 优先级2: desc内容查找
descContains("分享").visibleToUser().findOne(2000)

// 优先级3: className和属性组合查找
className("android.widget.LinearLayout")
    .clickable(true)
    .enabled(true)
    .focusable(true)
    .depth(3)
    .visibleToUser()
    .packageName("com.ss.android.ugc.aweme")
    .findOne(2000)
```

#### 6. 收藏按钮 (ID: dt6) - 智能收藏
```javascript
// 优先级1: desc关键词查找（推荐）
descContains("收藏").visibleToUser().findOne(2000)

// 优先级2: desc状态查找
descContains("未选中").visibleToUser().findOne(2000)

// 优先级3: 资源ID查找
id("com.ss.android.ugc.aweme:id/dt6").visibleToUser().findOne(2000)

// 智能收藏：只在未选中状态时点击
var favoriteButton = descContains("收藏").visibleToUser().findOne(2000);
if (favoriteButton && favoriteButton.desc().includes("未选中")) {
    favoriteButton.click(); // 只有未收藏时才点击
}
```

#### 7. 关注按钮 (ID: iwt) - 智能关注
```javascript
// 优先级1: desc内容查找（推荐）
desc("关注").visibleToUser().findOne(2000)

// 优先级2: desc关键词查找
descContains("关注").visibleToUser().findOne(2000)

// 优先级3: 资源ID查找
id("com.ss.android.ugc.aweme:id/iwt").visibleToUser().findOne(2000)

// 智能关注：优先desc查找，找不到表示已关注
var followButton = desc("关注").visibleToUser().findOne(2000);
if (followButton) {
    followButton.click(); // 找到关注按钮就点击
} else {
    console.log("未找到关注按钮，可能已关注"); // 找不到表示已关注
}
```

#### 8. 广告检测 - 智能识别
```javascript
// 基于文本和描述的广告检测
var adDetection = require('./ad_detection.js');

// 检测单个元素是否为广告
var isAd = adDetection.isAdElement(element);

// 检测当前屏幕是否包含广告
var hasAd = adDetection.hasAdOnScreen();

// 根据文本和描述直接检测
var isAdContent = adDetection.isAdByTextAndDesc("上海维继看视频", "上海维继看视频");

// 广告检测模式（支持多种广告类型）
// - 视频观看类: "看视频", "观看视频", "上海维继看视频"
// - 服务咨询类: "立即接单", "查看价格", "在线查价", "广告", "立即预约", "点击咨询", "立即合作"
// - 直播带货类: "直播间", "直播带货", "限时抢购", "主播推荐", "粉丝福利"
// - 教育培训类: "在线课程", "学习", "培训", "免费试听", "零基础", "包学会"
// - 美容健康类: "美容", "护肤", "减肥", "瘦身", "医美", "整形", "健康"
// - 招聘求职类: "招聘", "求职", "高薪", "兼职", "包吃住", "五险一金"
// - 房产汽车类: "买房", "租房", "装修", "汽车", "车贷", "房贷"
// - 社交交友类: "交友", "相亲", "脱单", "同城", "婚恋", "约会"
// - 赚钱类: "赚钱", "收益", "提现", "红包"
// - 游戏类: "游戏", "试玩", "下载游戏"
// - 购物类: "购买", "优惠", "促销", "秒杀"
// - 应用推广类: "下载", "APP", "注册"
// - 金融理财类: "理财", "投资", "贷款"
```

### 真人模拟特性

#### 随机延迟
- 点击前延迟: 100-400ms
- 点击后延迟: 200-600ms
- 输入后延迟: 500-1000ms

#### 随机偏移
- X轴偏移: ±5-10像素
- Y轴偏移: ±5像素
- 按压时长: 50-200ms

## ⚠️ 重要注意事项

### Auto.js属性访问
```javascript
// ✅ 正确：直接访问的属性
element.editable
element.checkable
element.checked

// ✅ 正确：方法调用的属性
element.id()
element.text()
element.enabled()
element.visibleToUser()
```

### 必须使用 visibleToUser()
```javascript
// ✅ 正确：确保控件可见
id("d5d").visibleToUser().findOne(2000)

// ❌ 错误：可能找到隐藏控件
id("d5d").findOne(2000)
```

### 查找时机
- **评论按钮**: 在视频页面随时可查找
- **输入框**: 需要先点击评论按钮，等待评论页面加载
- **发送按钮**: 需要先输入评论内容，按钮才会出现
- **关闭按钮**: 在评论页面、设置页面等子页面可查找
- **分享按钮**: 在视频页面随时可查找，通常在右侧
- **收藏按钮**: 在视频页面随时可查找，通常在右侧下方，建议使用智能收藏
- **关注按钮**: 在用户主页或视频页面可查找，优先desc查找，找不到表示已关注
- **广告检测**: 在任何页面都可以检测，建议在视频切换前检测以自动跳过广告

### 广告检测使用建议
- **检测时机**: 建议在观看视频前先检测是否为广告
- **处理方式**: 检测到广告时可选择快速跳过或记录信息
- **模式配置**: 可通过修改 `ad_patterns.js` 添加新的广告检测模式
- **性能考虑**: 广告检测会遍历屏幕元素，建议适度使用

## 🔧 故障排除

### 常见问题

#### 1. "editable不是函数"错误
```javascript
// ❌ 错误
element.editable()

// ✅ 正确
element.editable
```

#### 2. 找不到控件
- 检查是否在正确的页面
- 确认控件是否可见
- 验证抖音版本是否更新
- 查看控件属性是否变化

#### 3. 点击无效果
- 确认控件确实可点击
- 检查坐标计算是否正确
- 尝试使用备用点击方法

### 调试技巧

#### 1. 查看详细日志
```javascript
// 所有模块都有详细的console.log输出
// 可以通过日志了解查找过程
```

#### 2. 使用测试脚本
```javascript
// 运行对应的测试脚本查看详细信息
node test_comment_button_search.js
```

#### 3. 手动验证控件属性
```javascript
// 使用Auto.js的布局分析工具
// 或运行测试脚本查看控件属性
```

## 📈 性能优化

### 查找优化
- 按成功率排序查找方法
- 设置合理的超时时间
- 使用缓存避免重复查找

### 内存优化
- 及时释放控件引用
- 避免创建过多临时变量
- 合理使用sleep延迟

## 🔄 版本兼容性

### 支持的抖音版本
- 当前测试版本: 最新版本
- 兼容策略: 多重查找方法确保兼容性

### 更新维护
- 定期检查控件属性变化
- 根据用户反馈调整查找策略
- 保持与Auto.js版本同步

## 📞 技术支持

### 问题反馈
如遇到问题，请提供：
1. Auto.js版本
2. 抖音版本
3. 错误日志
4. 操作步骤

### 贡献指南
欢迎提交：
- Bug修复
- 功能改进
- 文档完善
- 测试用例

---

**免责声明**: 本项目仅供学习和研究使用，请遵守相关法律法规和平台规则。
