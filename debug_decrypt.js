// 调试解密过程的详细脚本

var licenseValidator = require("./license_validator.js");

console.log("=== 详细调试解密过程 ===");

// 目标信息
var targetKey = "7DCE-36A7-62EB-24F5";
var targetHash = "7dce36a762eb24f5";
var expectedDate = "2025-06-06";
var secretKey = "黑猫AI-secret-key-123";

console.log("目标秘钥: " + targetKey);
console.log("目标哈希: " + targetHash);
console.log("期望日期: " + expectedDate);
console.log("密钥字符串: " + secretKey);

// 手动测试期望的组合
console.log("\n=== 手动测试期望组合 ===");
var expectedCombination = "20250606" + secretKey;
console.log("期望组合: " + expectedCombination);

var expectedHash = licenseValidator.md5(expectedCombination);
console.log("期望组合的MD5: " + expectedHash);
console.log("前16位: " + expectedHash.substring(0, 16));
console.log("与目标匹配: " + (expectedHash.substring(0, 16) === targetHash ? "✅" : "❌"));

if (expectedHash.substring(0, 16) !== targetHash) {
    console.log("❌ 期望组合不匹配！让我们尝试其他可能性...");
    
    // 尝试不同的日期格式
    var dateFormats = [
        "20250606",
        "2025-06-06", 
        "2025/06/06",
        "06/06/2025",
        "06-06-2025",
        "250606",
        "25606"
    ];
    
    console.log("\n=== 尝试不同日期格式 ===");
    for (var i = 0; i < dateFormats.length; i++) {
        var dateFormat = dateFormats[i];
        var combination = dateFormat + secretKey;
        var hash = licenseValidator.md5(combination);
        var matches = hash.substring(0, 16) === targetHash;
        
        console.log("格式: " + dateFormat);
        console.log("  组合: " + combination);
        console.log("  哈希: " + hash.substring(0, 16));
        console.log("  匹配: " + (matches ? "✅" : "❌"));
        
        if (matches) {
            console.log("🎉 找到正确的日期格式: " + dateFormat);
            break;
        }
    }
    
    // 尝试不同的密钥变体
    var keyVariants = [
        "黑猫AI-secret-key-123",
        "黑猫AI-SECRET-KEY-123",
        "heimaoai-secret-key-123",
        "secret-key-123",
        "黑猫AI"
    ];
    
    console.log("\n=== 尝试不同密钥变体 ===");
    for (var j = 0; j < keyVariants.length; j++) {
        var keyVariant = keyVariants[j];
        var combination = "20250606" + keyVariant;
        var hash = licenseValidator.md5(combination);
        var matches = hash.substring(0, 16) === targetHash;
        
        console.log("密钥: " + keyVariant);
        console.log("  组合: " + combination);
        console.log("  哈希: " + hash.substring(0, 16));
        console.log("  匹配: " + (matches ? "✅" : "❌"));
        
        if (matches) {
            console.log("🎉 找到正确的密钥: " + keyVariant);
            break;
        }
    }
} else {
    console.log("✅ 期望组合匹配！现在测试完整解密...");
    
    // 执行完整解密
    console.log("\n=== 执行完整解密 ===");
    var result = licenseValidator.validateLicense(targetKey);
    console.log("解密结果: " + JSON.stringify(result, null, 2));
    
    if (result.success && result.expireDate === expectedDate) {
        console.log("🎉 完整解密成功！");
    } else {
        console.log("❌ 完整解密失败");
        console.log("期望: " + expectedDate);
        console.log("实际: " + (result.expireDate || "null"));
    }
}

// 显示字符编码信息
console.log("\n=== 字符编码信息 ===");
console.log("密钥字符串长度: " + secretKey.length);
for (var k = 0; k < secretKey.length; k++) {
    var char = secretKey.charAt(k);
    var code = secretKey.charCodeAt(k);
    console.log("字符 '" + char + "': " + code);
}
