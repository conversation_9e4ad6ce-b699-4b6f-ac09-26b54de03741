// 抖音自动刷视频脚本
// 功能：启动抖音，随机观看5-30秒，然后上滑切换下一个视频

// 简单的日志输出，不使用复杂的重定向
console.log("douyin.js脚本已加载，正在初始化...");

//记录评论数
var getNumber = 0; // 默认值，将从文件中加载实际值

// 添加一个标志变量，用于控制脚本停止
var isRunning = true;

// 定义全局随机时间变量（默认值）
var likeTimeMin = 3; // 点赞最小时间 3秒
var likeTimeMax = 17; // 点赞最大时间 17秒
var commentTimeMin = 4; // 留言最小时间 4秒
var commentTimeMax = 16; // 留言最大时间 16秒
var watchTimeMin = 5; // 观看最小时间 5秒
var watchTimeMax = 15; // 观看最大时间 15秒

// 定义全局概率变量（默认值）
var actionProbability = 70; // 点赞和留言的执行概率 70%

// 定义全局休息机制变量（默认值）
var restEnabled = true; // 是否启用休息机制
var watchCountMin = 15; // 最少观看视频数量
var watchCountMax = 45; // 最多观看视频数量
var restTimeMin = 3; // 最短休息时间（分钟）
var restTimeMax = 15; // 最长休息时间（分钟）

// 休息机制运行时变量
var videoWatchCount = 0; // 当前已观看视频计数
var targetWatchCount = 0; // 本轮目标观看数量

// 定义全局AI提示词变量（默认值）
var aiPromptWithDesc = "你是一个抖音用户，正在观看一个视频，视频简介是：{description}。请根据视频简介生成一条真实自然的评论，要求15字以内，带表情符号，语气要亲切自然。"; // 有视频介绍的AI提示词
var aiPromptWithoutDesc = "你是一个抖音用户，正在观看一个有趣的视频，请生成一条通用的正面评论，要求15字以内，带表情符号，语气要亲切自然。"; // 没有视频介绍的AI提示词
var customComments = [
    "哇，这个视频太棒了！👍", // 通用赞美
    "看得我都想点赞了 ❤️", // 表达喜爱
    "真的很不错呢 😊", // 简单肯定
    "这个我喜欢！✨", // 表达喜好
    "太有意思了 😄", // 表达有趣
    "看了好几遍了 👀", // 表达重复观看
    "必须给个赞！👏", // 表达支持
    "这个创意不错 💡", // 赞美创意
    "看着就很开心 😃", // 表达心情
    "真的很棒！🔥", // 热情赞美
    "学到了！📚", // 表达学习
    "太治愈了 🌸", // 表达治愈感
    "看着就很舒服 😌", // 表达舒适感
    "这个角度很棒 📸", // 赞美拍摄
    "音乐也很好听 🎵" // 赞美音乐
]; // 自备评论库数组

// 定义getNumber数据文件路径
var GET_NUMBER_FILE = files.cwd() + "/douyin_get_number.txt"; // 评论数量记录文件

// 从文件加载getNumber数值
function loadGetNumber() {
    console.log("开始加载评论数量记录..."); // 开始加载日志

    try {
        if (files.exists(GET_NUMBER_FILE)) {
            var numberStr = files.read(GET_NUMBER_FILE); // 读取文件内容
            var savedNumber = parseInt(numberStr.trim()); // 转换为整数

            if (!isNaN(savedNumber) && savedNumber >= 0) {
                getNumber = savedNumber; // 更新全局变量
                console.log("✓ 成功加载评论数量: " + getNumber); // 成功日志
                events.broadcast.emit("douyin_get", getNumber); // 发送事件更新UI
            } else {
                console.log("⚠️ 文件中的数值无效，使用默认值0"); // 警告日志
                getNumber = 0; // 使用默认值
            }
        } else {
            console.log("⚠️ 评论数量文件不存在，使用默认值0"); // 文件不存在日志
            getNumber = 0; // 使用默认值
        }
    } catch (error) {
        console.error("✗ 加载评论数量失败: " + error); // 错误日志
        getNumber = 0; // 使用默认值
    }

    return getNumber; // 返回加载的数值
}

// 保存getNumber数值到文件
function saveGetNumber() {
    console.log("保存评论数量到文件: " + getNumber); // 保存日志

    try {
        files.write(GET_NUMBER_FILE, getNumber.toString()); // 写入文件
        console.log("✓ 评论数量保存成功"); // 成功日志
        return true; // 返回成功
    } catch (error) {
        console.error("✗ 保存评论数量失败: " + error); // 错误日志
        return false; // 返回失败
    }
}

// 增加评论数量并保存
function incrementGetNumber() {
    getNumber++; // 增加数量
    console.log("评论数量增加到: " + getNumber); // 增加日志
    events.broadcast.emit("douyin_get", getNumber); // 发送事件更新UI
    saveGetNumber(); // 保存到文件
    return getNumber; // 返回当前数量
}

// 重置评论数量
function resetGetNumber() {
    console.log("重置评论数量为0"); // 重置日志
    getNumber = 0; // 重置为0
    events.broadcast.emit("douyin_get", getNumber); // 发送事件更新UI
    saveGetNumber(); // 保存到文件
    return getNumber; // 返回当前数量
}

// 从设置文件加载时间设置和AI提示词设置
function loadAllSettings() {
    // 尝试多个可能的路径
    var possiblePaths = [
        files.cwd() + "/douyin_settings.json",
        // files.path("./douyin_settings.json"),
        // files.getSdcardPath() + "/脚本/douyin_settings.json",
        // "/sdcard/脚本/douyin_settings.json"
    ];

    var settingsLoaded = false;

    // 尝试从每个可能的路径加载设置
    for (var i = 0; i < possiblePaths.length; i++) {
        var settingsFile = possiblePaths[i];
        console.log("尝试从路径加载设置: " + settingsFile);

        if (files.exists(settingsFile)) {
            try {
                var settingsStr = files.read(settingsFile);
                var settings = JSON.parse(settingsStr);

                // 更新全局变量
                likeTimeMin = settings.likeTimeMin || likeTimeMin;
                likeTimeMax = settings.likeTimeMax || likeTimeMax;
                commentTimeMin = settings.commentTimeMin || commentTimeMin;
                commentTimeMax = settings.commentTimeMax || commentTimeMax;
                watchTimeMin = settings.watchTimeMin || watchTimeMin;
                watchTimeMax = settings.watchTimeMax || watchTimeMax;

                // 更新概率设置
                actionProbability = settings.actionProbability || actionProbability;

                // 更新休息机制设置
                restEnabled = settings.restEnabled !== undefined ? settings.restEnabled : restEnabled;
                watchCountMin = settings.watchCountMin || watchCountMin;
                watchCountMax = settings.watchCountMax || watchCountMax;
                restTimeMin = settings.restTimeMin || restTimeMin;
                restTimeMax = settings.restTimeMax || restTimeMax;

                // 更新AI提示词设置
                aiPromptWithDesc = settings.aiPromptWithDesc || aiPromptWithDesc;
                aiPromptWithoutDesc = settings.aiPromptWithoutDesc || aiPromptWithoutDesc;

                // 处理自备评论库（从字符串转换为数组）
                if (settings.customComments && settings.customComments.trim() !== "") {
                    customComments = settings.customComments.split('\n').filter(function (comment) {
                        return comment.trim() !== ""; // 过滤空行
                    });
                    console.log("已加载 " + customComments.length + " 条自备评论");
                } else {
                    customComments = []; // 如果没有自备评论，使用空数组
                    console.log("未设置自备评论，将使用AI模块默认评论");
                }

                console.log("已从 " + settingsFile + " 加载所有设置：");
                console.log("点赞时间: " + likeTimeMin + "-" + likeTimeMax + "秒");
                console.log("留言时间: " + commentTimeMin + "-" + commentTimeMax + "秒");
                console.log("观看时间: " + watchTimeMin + "-" + watchTimeMax + "秒");
                console.log("执行概率: " + actionProbability + "% (同时控制点赞和留言)");
                console.log("休息机制: " + (restEnabled ? "启用" : "禁用"));
                if (restEnabled) {
                    console.log("观看数量: " + watchCountMin + "-" + watchCountMax + "个视频");
                    console.log("休息时长: " + restTimeMin + "-" + restTimeMax + "分钟");
                }
                console.log("有简介AI提示词: " + (aiPromptWithDesc.length > 50 ? aiPromptWithDesc.substring(0, 50) + "..." : aiPromptWithDesc));
                console.log("无简介AI提示词: " + (aiPromptWithoutDesc.length > 50 ? aiPromptWithoutDesc.substring(0, 50) + "..." : aiPromptWithoutDesc));

                settingsLoaded = true;
                break; // 成功加载设置后退出循环
            } catch (e) {
                console.error("从 " + settingsFile + " 加载设置失败: " + e);
            }
        } else {
            console.log("未找到设置文件: " + settingsFile);
        }
    }

    if (!settingsLoaded) {
        console.log("所有路径都未找到设置文件，使用默认设置");
        console.log("使用默认AI提示词和自备评论设置");
    }

    // 将秒转换为毫秒
    likeTimeMin *= 1000;
    likeTimeMax *= 1000;
    commentTimeMin *= 1000;
    commentTimeMax *= 1000;
    watchTimeMin *= 1000;
    watchTimeMax *= 1000;
}

// 加载所有设置（时间设置和AI提示词设置）
loadAllSettings();

// 初始化休息机制
function initRestMechanism() {
    if (restEnabled) {
        // 生成本轮目标观看数量
        targetWatchCount = random(watchCountMin, watchCountMax);
        console.log("休息机制已启用，本轮目标观看 " + targetWatchCount + " 个视频");
        events.broadcast.emit("douyin_status", "目标观看: " + targetWatchCount + "个视频");
    } else {
        console.log("休息机制已禁用");
    }
}

// 检查是否需要休息
function shouldTakeRest() {
    if (!restEnabled) return false; // 如果休息机制未启用，不休息
    return videoWatchCount >= targetWatchCount; // 如果达到目标观看数量，需要休息
}

// 执行休息流程
function takeRest() {
    console.log("开始休息，已观看 " + videoWatchCount + " 个视频");
    events.broadcast.emit("douyin_status", "开始休息");

    // 生成随机休息时长（分钟转毫秒）
    var restTime = random(restTimeMin, restTimeMax);
    var restTimeMs = restTime * 60 * 1000;

    console.log("休息时长: " + restTime + " 分钟");
    events.broadcast.emit("douyin_status", "休息 " + restTime + " 分钟");

    // 切换到桌面
    console.log("切换到手机桌面");
    home(); // 返回桌面
    sleep(2000); // 等待桌面加载

    // 休息等待
    console.log("开始休息等待...");
    sleep(restTimeMs);

    // 休息结束，重新启动抖音
    console.log("休息结束，重新启动抖音");
    events.broadcast.emit("douyin_status", "休息结束，重启抖音");

    // 重置计数器和目标
    videoWatchCount = 0;
    targetWatchCount = random(watchCountMin, watchCountMax);
    console.log("重置计数器，新目标观看 " + targetWatchCount + " 个视频");

    // 重新启动抖音
    launchDouyin();
    sleep(3000); // 等待抖音加载
}

// 初始化休息机制
initRestMechanism();

// 加载评论数量记录
loadGetNumber();

// 监听脚本停止事件
events.on('exit', function () {
    isRunning = false;
    console.log("抖音脚本已接收到停止信号");
    events.broadcast.emit("douyin_status", "已经停止");
});


// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务");
    auto.waitFor();
}

// 主函数
function main() {
    // 启动抖音
    launchDouyin();
    // 等待抖音加载完成
    sleep(3000);
    // 确保抖音完全加载
    toast("抖音自动刷视频脚本已启动");
    console.log("脚本开始运行...");
    // 发送脚本启动事件
    events.broadcast.emit("douyin_status", '抖音脚本已启动');
    // 循环刷视频
    while (isRunning) {
        //广告判断
        if (!isRunning) break;
        var gg = textEndsWith('广告').visibleToUser().findOne(2000);
        var zb = textContains('直播中').visibleToUser().findOne(2000);
        if (gg != null || zb != null) {
            events.broadcast.emit("douyin_status", '广告直播跳过');
            swipeUp();
            // 发送滑动切换视频的事件
            events.broadcast.emit("douyin_status", '已切换下一个视频');
            continue;
        }


        if (!isRunning) break;
        // 发送正在观看视频的事件
        events.broadcast.emit("douyin_status", '正在观看视频');

        // 分段等待，便于及时响应停止信号
        if (!isRunning) break;
        // 生成随机时间（毫秒）
        var likeTime = random(likeTimeMin, likeTimeMax);
        var commentTime = random(commentTimeMin, commentTimeMax);
        var randomWatchTime = random(watchTimeMin, watchTimeMax);

        // 第一层概率判断：是否点赞
        var shouldLike = (Math.random() * 100) < actionProbability; // 根据设置的概率决定是否点赞
        if (shouldLike) {
            // 执行点赞
            if (!isRunning) break;
            console.log("执行点赞 (概率: " + actionProbability + "%)");
            console.log("随机点赞时间: " + (likeTime / 1000) + "秒");
            sleep(likeTime); // 随机点赞的时间
            if (!isRunning) break;
            events.broadcast.emit("douyin_status", '开始点赞');
            //调用like.js模块
            var like = require("./douyinYuansu/click_like_button.js");
            var pd = like.toLike();//如果没有找到
            if (pd == null) {
                swipeUp();
                // 发送滑动切换视频的事件
                events.broadcast.emit("douyin_status", '已切换下一个视频');
                continue;
            }
            events.broadcast.emit("douyin_status", '点赞完成');

            if (!isRunning) break;

            // 第二层概率判断：在已点赞的基础上，是否留言
            var shouldComment = (Math.random() * 100) < actionProbability; // 再次使用同一个概率判断是否留言
            if (shouldComment) {
                // 执行留言
                console.log("执行留言 (概率: " + actionProbability + "%, 已点赞基础上)");
                events.broadcast.emit("douyin_status", "准备留言");
                console.log("随机留言时间: " + (commentTime / 1000) + "秒");
            sleep(commentTime);
            //获取简介文字
            var commentText = require('./douyinYuansu/comment.js')
            var videoDescription = commentText.getUserDescription().replace('#', ' ')
            events.broadcast.emit("douyin_status", "简介: " + (videoDescription.length > 20 ? videoDescription.slice(0, 20) : videoDescription));

            // 判断简介是否为空，决定调用哪个AI提示词
            var finalAiPrompt = ""; // 最终使用的AI提示词
            var hasDescription = videoDescription && videoDescription.trim() !== ""; // 检查是否有简介

            if (hasDescription) {
                // 有简介：使用有简介的AI提示词，并替换{description}占位符
                finalAiPrompt = aiPromptWithDesc.replace("{description}", videoDescription);
                console.log("✓ 视频有简介，使用有简介的AI提示词");
            } else {
                // 没有简介：使用没有简介的AI提示词
                finalAiPrompt = aiPromptWithoutDesc;
                console.log("✓ 视频没有简介，使用通用AI提示词");
            }

            sleep(commentTime/2); // 随机留言时间

            if (!isRunning) break;
            //点击评论按钮
            var comment_button = require('./douyinYuansu/click_comment_button.js');
            comment_button.clickCommentButton()
            sleep(commentTime/2)

            if (!isRunning) break;
            //点击评论框
            var comment_input = require('./douyinYuansu/click_comment_input.js')
            comment_input.clickCommentInput()
            if (!isRunning) break;
            events.broadcast.emit("douyin_status", "留言了，等待" + (commentTime / 1000) + "秒");
            console.log("随机观看时间: " + (randomWatchTime / 1000) + "秒");
            sleep(commentTime/3)

            //AI生成评论 - 使用选择的提示词和自备评论库
            var ai = require('./ai.js')
            console.log("调用AI生成评论，提示词长度: " + finalAiPrompt.length + "，自备评论数量: " + customComments.length);
            var content = ai.generateComment(finalAiPrompt, customComments) // 传入选择的AI提示词和自备评论库
            events.broadcast.emit("douyin_status", "评论: " + (content.length > 20 ? content.slice(0, 20) : content))
            if (!isRunning) break;
            sleep(randomWatchTime/3); // 随机观看的时长

            //输入评论
            var inputText = comment_input.findCommentInput();
            inputText.setText(content)
            if (!isRunning) break;
            sleep(randomWatchTime/3); // 随机观看的时长

            //发送评论
            var send_input = require('./douyinYuansu/click_send_button.js')
            send_input.clickSendButton()
            events.broadcast.emit("douyin_status", "成功发送评论")
            if (!isRunning) break;
            sleep(randomWatchTime/2); // 随机观看的时长

            //关闭评论
            var close_comment = require('./douyinYuansu/click_close_button.js')
            close_comment.clickCloseButton()
            events.broadcast.emit("douyin_status", "关闭评论")
                //更新获客数量并保存到文件
                incrementGetNumber(); // 增加评论数量并自动保存
            } else {
                // 点赞了但不留言
                console.log("跳过留言 (概率: " + actionProbability + "%, 已点赞但随机数未命中)");
                events.broadcast.emit("douyin_status", '只点赞不留言');
                // 等待一段时间模拟观看
                sleep(commentTime / 2); // 等待一半的留言时间
            }
        } else {
            // 不点赞（也就不会留言）
            console.log("跳过点赞和留言 (概率: " + actionProbability + "%, 随机数未命中)");
            events.broadcast.emit("douyin_status", '跳过所有操作');
            // 即使不执行操作，也要等待一段时间，模拟观看
            sleep((likeTime + commentTime) / 3); // 等待一部分时间模拟观看
        }

        if (!isRunning) break;
        sleep(randomWatchTime); // 随机观看的时长
        if (!isRunning) break;

        // 增加观看计数
        videoWatchCount++;
        console.log("已观看 " + videoWatchCount + "/" + targetWatchCount + " 个视频");
        events.broadcast.emit("douyin_status", "已观看 " + videoWatchCount + "/" + targetWatchCount);

        // 检查是否需要休息
        if (shouldTakeRest()) {
            takeRest(); // 执行休息流程
            // 休息后会重新启动抖音，继续循环
            continue;
        }

        // 上滑切换到下一个视频
        swipeUp();
        // 发送滑动切换视频的事件
        events.broadcast.emit("douyin_status", '已经切换下一个视频');
        // 短暂等待加载下一个视频
        sleep(1000);
    }
}

// 启动抖音
function launchDouyin() {
    console.log("正在启动抖音...");
    if (!launchApp("抖音")) {
        console.error("未安装抖音或无法启动");
        exit();
    }
}

// 引入真人上滑模块
var swipeModule = require("./douyinYuansu/swipe_up_human.js");

// 上滑操作 - 使用真人模拟上滑模块
function swipeUp() {
    console.log("上滑切换下一个视频");

    try {
        // 使用智能上滑模式，自动选择最合适的滑动方式
        var success = swipeModule.smartSwipeUp();

        if (success) {
            console.log("✓ 真人模拟上滑操作成功");
        } else {
            console.log("✗ 真人模拟上滑操作失败，使用备用方案");
            // 备用方案：使用原来的简单上滑
            fallbackSwipeUp();
        }
    } catch (error) {
        console.error("上滑模块调用失败: " + error);
        console.log("使用备用上滑方案");
        // 备用方案：使用原来的简单上滑
        fallbackSwipeUp();
    }
}

// 备用上滑操作（保留原来的简单上滑作为备用）
function fallbackSwipeUp() {
    console.log("执行备用上滑操作");

    // 获取屏幕尺寸
    let width = device.width;
    let height = device.height;

    // 添加随机性，避免被检测
    let startX = random(width * 0.45, width * 0.55); // 缩小水平范围，更居中
    let startY = random(height * 0.8, height * 0.9);  // 起点位置调高
    let endY = random(height * 0.1, height * 0.2);    // 终点位置调低
    let duration = random(300, 400);  // 缩小滑动时间范围，使动作更稳定

    // 执行上滑操作
    swipe(startX, startY, startX, endY, duration);
}

// 随机数生成函数
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
// 运行主函数
//刷抖音点赞留言
main()
