/**
 * 点击抖音留言输入框模块
 * 根据控件属性定位并模拟真人点击留言输入框
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找留言输入框的主函数
 * @returns {UiObject|null} 找到的留言输入框控件，未找到返回null
 */
function findCommentInput() {
    console.log("开始查找留言输入框..."); // 输出开始查找日志

    try {
        // 方法1: 通过完整的资源ID查找（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var inputByFullId = id("com.ss.android.ugc.aweme:id/dyd").visibleToUser().findOne(3000); // 使用完整的资源ID，确保可见，等待3秒
        if (inputByFullId) {
            console.log("✓ 通过完整资源ID找到留言输入框"); // 输出找到输入框的日志
            return inputByFullId; // 返回找到的输入框
        }

        // 方法2: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var inputByShortId = id("dyd").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (inputByShortId) {
            console.log("✓ 通过简短ID找到留言输入框"); // 输出找到输入框的日志
            return inputByShortId; // 返回找到的输入框
        }

        // 方法3: 通过text内容查找
        console.log("方法3: 尝试通过text内容查找..."); // 输出当前尝试的方法
        var inputByText = text("善恶终有报，恶言伤人心").visibleToUser().findOne(2000); // 通过完整text查找
        if (inputByText) {
            console.log("✓ 通过text内容找到留言输入框"); // 输出找到输入框的日志
            return inputByText; // 返回找到的输入框
        }

        // 方法4: 通过text关键词查找
        console.log("方法4: 尝试通过text关键词查找..."); // 输出当前尝试的方法
        var inputByTextContains = textContains("善恶终有报").visibleToUser().findOne(2000); // 查找text包含关键词的控件
        if (inputByTextContains) {
            console.log("✓ 通过text关键词找到留言输入框，text: " + inputByTextContains.text()); // 输出找到输入框的日志
            return inputByTextContains; // 返回找到的输入框
        }

        // 方法5: 通过text包含"恶言伤人心"查找
        console.log("方法5: 尝试通过text包含'恶言伤人心'查找..."); // 输出当前尝试的方法
        var inputByTextContains2 = textContains("恶言伤人心").visibleToUser().findOne(2000); // 查找text包含"恶言伤人心"的控件
        if (inputByTextContains2) {
            console.log("✓ 通过text关键词找到留言输入框，text: " + inputByTextContains2.text()); // 输出找到输入框的日志
            return inputByTextContains2; // 返回找到的输入框
        }

        // 方法6: 通过属性组合查找
        console.log("方法6: 尝试通过属性组合查找..."); // 输出当前尝试的方法
        var inputByProps = editable(true) // 可编辑
            .enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .longClickable(true) // 可长按
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒

        if (inputByProps) {
            // 进一步验证text内容
            var propsText = inputByProps.text(); // 获取控件文本
            if (propsText && (propsText.includes("善恶") || propsText.includes("恶言"))) {
                console.log("✓ 通过属性组合找到留言输入框，text: " + propsText); // 输出找到输入框的日志
                return inputByProps; // 返回找到的输入框
            }
        }

        // 方法7: 遍历所有可编辑控件查找
        console.log("方法7: 遍历所有可编辑控件查找..."); // 输出当前尝试的方法
        var allEditableViews = editable(true).visibleToUser().find(); // 查找所有可见的可编辑控件
        console.log("找到 " + allEditableViews.length + " 个可见的可编辑控件"); // 输出找到的控件数量

        for (var i = 0; i < allEditableViews.length; i++) {
            var element = allEditableViews[i]; // 遍历每个控件
            var elementId = element.id(); // 获取控件ID
            var elementText = element.text(); // 获取控件文本
            var elementPackage = element.packageName(); // 获取包名

            // 检查是否符合留言输入框的特征
            if (elementPackage && elementPackage.includes("aweme") &&
                ((elementId && elementId.includes("dyd")) ||
                 (elementText && (elementText.includes("善恶") || elementText.includes("恶言"))))) {
                console.log("✓ 通过遍历找到留言输入框 - ID: " + elementId + ", text: " + elementText); // 输出找到输入框的日志
                return element; // 返回找到的输入框
            }
        }

        console.log("✗ 所有方法都未找到留言输入框"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 查找留言输入框时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} input - 要点击的输入框控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(input) {
    console.log("开始模拟真人点击输入框操作..."); // 输出开始点击日志

    try {
        // 获取输入框的位置信息
        var bounds = input.bounds(); // 获取输入框边界
        if (!bounds) {
            console.error("✗ 无法获取输入框位置信息"); // 输出错误信息
            return false; // 返回失败
        }

        console.log("输入框位置: " + bounds.toString()); // 输出输入框位置

        // 计算输入框中心点坐标
        var centerX = bounds.centerX(); // 输入框中心X坐标
        var centerY = bounds.centerY(); // 输入框中心Y坐标

        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-10, 10); // X轴随机偏移-10到10像素
        var randomOffsetY = random(-5, 5); // Y轴随机偏移-5到5像素

        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标

        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标

        // 模拟真人的点击前停顿
        var preClickDelay = random(100, 300); // 点击前随机延迟100-300毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟

        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = press(clickX, clickY, random(50, 150)); // 执行坐标点击，随机按压时长

        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = input.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }

        // 模拟真人的点击后停顿
        var postClickDelay = random(200, 500); // 点击后随机延迟200-500毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟

        console.log("✓ 模拟真人点击输入框操作完成"); // 输出完成日志
        return true; // 返回成功

    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击留言输入框
 * @returns {boolean} 操作是否成功
 */
function clickCommentInput() {
    console.log("=== 开始执行点击留言输入框操作 ==="); // 输出开始操作日志

    // 检查是否在抖音应用中
    // if (!currentPackage().includes("aweme")) {
    //     console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
    //     launchApp("抖音"); // 启动抖音应用
    //     sleep(3000); // 等待3秒让应用加载
    //     waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    // }

    // 查找留言输入框
    var commentInput = findCommentInput(); // 调用查找函数

    if (!commentInput) {
        console.error("✗ 未找到留言输入框，操作失败"); // 输出失败信息
        toast("未找到留言输入框"); // 显示失败提示
        return false; // 返回失败
    }

    console.log("✓ 成功找到留言输入框"); // 输出成功找到日志
    toast("找到留言输入框，准备点击"); // 显示找到提示

    // 执行模拟真人点击
    var clickResult = humanLikeClick(commentInput); // 调用点击函数

    if (clickResult) {
        console.log("✓ 留言输入框点击成功"); // 输出成功日志
        toast("✓ 留言输入框点击成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 留言输入框点击失败"); // 输出失败日志
        toast("✗ 留言输入框点击失败"); // 显示失败提示
        return false; // 返回失败
    }
}

/**
 * 测试函数：测试查找和点击功能
 */
function testClickCommentInput() {
    console.log("=== 开始测试点击留言输入框功能 ==="); // 输出测试开始信息

    // 测试查找功能
    console.log("\n📝 测试1: 查找留言输入框"); // 输出测试类型
    var input = findCommentInput(); // 查找输入框
    if (input) {
        console.log("✓ 查找测试成功"); // 输出测试成功
        console.log("输入框信息: ID=" + input.id() + ", text=" + input.text()); // 输出输入框信息
        console.log("输入框详细属性:"); // 输出详细属性标题
        console.log("  - className: " + input.className()); // 输出类名
        console.log("  - editable: " + input.editable); // 输出可编辑状态
        console.log("  - enabled: " + input.enabled()); // 输出启用状态
        console.log("  - focusable: " + input.focusable()); // 输出可获得焦点状态
        console.log("  - longClickable: " + input.longClickable()); // 输出可长按状态
        console.log("  - visibleToUser: " + input.visibleToUser()); // 输出可见性状态
        console.log("  - bounds: " + input.bounds()); // 输出边界信息
    } else {
        console.log("✗ 查找测试失败"); // 输出测试失败
    }

    // 测试完整流程
    console.log("\n📝 测试2: 完整点击流程"); // 输出测试类型
    var result = clickCommentInput(); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果

    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

// 导出模块，供其他脚本调用
module.exports = {
    findCommentInput: findCommentInput, // 导出查找留言输入框函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickCommentInput: clickCommentInput, // 导出主函数
    testClickCommentInput: testClickCommentInput // 导出测试函数
};

// 如果直接运行此脚本，则执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("直接运行click_comment_input.js，开始执行测试..."); // 输出直接运行提示
    testClickCommentInput(); // 执行测试函数
}
