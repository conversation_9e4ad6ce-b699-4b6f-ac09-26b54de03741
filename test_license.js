// AutoJS 测试秘钥验证功能的脚本

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 秘钥验证功能测试 ===");
toast("开始测试秘钥验证功能");

// 测试用例1: 空秘钥
console.log("测试1: 空秘钥");
var result1 = licenseValidator.validateLicense("");
console.log("结果:", JSON.stringify(result1));

// 测试用例2: 格式错误的秘钥
console.log("测试2: 格式错误的秘钥");
var result2 = licenseValidator.validateLicense("INVALID-KEY");
console.log("结果:", JSON.stringify(result2));

// 测试用例3: 您提供的第一个测试秘钥
console.log("测试3: 828E-72BA-C78E-FDA8 (应该解密为 2025-06-08)");
var result3 = licenseValidator.validateLicense("828E-72BA-C78E-FDA8");
console.log("结果:", JSON.stringify(result3));

// 测试用例4: 您提供的第二个测试秘钥
console.log("测试4: 9EA3-27F7-6094-312F (应该解密为 2025-06-06)");
var result4 = licenseValidator.validateLicense("9EA3-27F7-6094-312F");
console.log("结果:", JSON.stringify(result4));

// 测试用例5: 随机格式正确的秘钥
console.log("测试5: 随机格式正确的秘钥");
var result5 = licenseValidator.validateLicense("ABCD-1234-EFGH-5678");
console.log("结果:", JSON.stringify(result5));

// 测试用例6: 测试保存和加载功能
console.log("测试6: 保存和加载功能");
var testKey = "828E-72BA-C78E-FDA8";
licenseValidator.saveLicenseToConfig(testKey);
var loadedKey = licenseValidator.loadLicenseFromConfig();
console.log("保存的秘钥:", testKey);
console.log("加载的秘钥:", loadedKey);
console.log("保存加载是否一致:", testKey === loadedKey);

console.log("=== 测试完成 ===");
toast("测试完成，请查看控制台日志");
