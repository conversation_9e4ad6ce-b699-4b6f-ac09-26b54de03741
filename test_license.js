// AutoJS 测试秘钥验证功能的脚本

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 秘钥验证功能测试 ===");
toast("开始测试秘钥验证功能");

// 测试用例1: 空秘钥
console.log("测试1: 空秘钥");
var result1 = licenseValidator.validateLicense("");
console.log("结果:", JSON.stringify(result1));

// 测试用例2: 格式错误的秘钥
console.log("测试2: 格式错误的秘钥");
var result2 = licenseValidator.validateLicense("INVALID-KEY");
console.log("结果:", JSON.stringify(result2));

// 测试用例3: 正确格式的秘钥
console.log("测试3: 正确格式的秘钥");
var result3 = licenseValidator.validateLicense("1234-5678-9ABC-DEF0");
console.log("结果:", JSON.stringify(result3));

// 测试用例4: 另一个正确格式的秘钥
console.log("测试4: 另一个正确格式的秘钥");
var result4 = licenseValidator.validateLicense("ABCD-1234-EFGH-5678");
console.log("结果:", JSON.stringify(result4));

// 测试用例5: 测试保存和加载功能
console.log("测试5: 保存和加载功能");
var testKey = "TEST-1234-ABCD-5678";
licenseValidator.saveLicenseToConfig(testKey);
var loadedKey = licenseValidator.loadLicenseFromConfig();
console.log("保存的秘钥:", testKey);
console.log("加载的秘钥:", loadedKey);
console.log("保存加载是否一致:", testKey === loadedKey);

console.log("=== 测试完成 ===");
toast("测试完成，请查看控制台日志");
