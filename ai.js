/**
 * AI评论生成模块
 * 基于讯飞星火API生成抖音评论
 */

// AI接口配置
const AI_CONFIG = {
    url: "https://spark-api-open.xf-yun.com/v1/chat/completions", // API接口地址
    token: "Bearer aGKhRVyeXUdFWgBJmMJq:YEhChpUHURUGSNLkKhqJ", // API授权令牌
    model: "generalv3", // 使用的AI模型
    maxTokens: 8192, // 最大令牌数
    topK: 6, // Top-K采样参数
    temperature: 1 // 温度参数，控制生成的随机性
};

// 备用评论模板（用于视频没有简介或网络失败的情况）
const FALLBACK_COMMENTS = [
    "哇，这个视频太棒了！👍", // 通用赞美
    "看得我都想点赞了 ❤️", // 表达喜爱
    "真的很不错呢 😊", // 简单肯定
    "这个我喜欢！✨", // 表达喜好
    "太有意思了 😄", // 表达有趣
    "看了好几遍了 👀", // 表达重复观看
    "必须给个赞！👏", // 表达支持
    "这个创意不错 💡", // 赞美创意
    "看着就很开心 😃", // 表达心情
    "真的很棒！🔥", // 热情赞美
    "学到了！📚", // 表达学习
    "太治愈了 🌸", // 表达治愈感
    "看着就很舒服 😌", // 表达舒适感
    "这个角度很棒 📸", // 赞美拍摄
    "音乐也很好听 🎵" // 赞美音乐
];

/**
 * 生成备用评论（用于视频没有简介的情况）
 * @param {Array} customComments - 自定义备用评论数组（可选）
 * @returns {string} 随机选择的备用评论
 */
function generateFallbackComment(customComments) {
    console.log("使用备用评论模板生成评论"); // 输出日志

    // 使用传入的自定义评论或默认评论模板
    var commentsToUse = customComments && customComments.length > 0 ? customComments : FALLBACK_COMMENTS; // 选择使用的评论库

    console.log("使用的评论库大小: " + commentsToUse.length); // 输出评论库大小
    if (customComments && customComments.length > 0) {
        console.log("✓ 使用外部传入的自定义评论模板"); // 输出使用自定义模板的日志
    } else {
        console.log("✓ 使用默认备用评论模板"); // 输出使用默认模板的日志
    }

    // 随机选择一个备用评论
    var randomIndex = Math.floor(Math.random() * commentsToUse.length); // 生成随机索引
    var selectedComment = commentsToUse[randomIndex]; // 选择评论

    console.log("✓ 选择的备用评论: " + selectedComment); // 输出选择的评论
    return selectedComment; // 返回评论
}

/**
 * 生成抖音评论的主函数（唯一对外接口）
 * @param {string} aiPrompt - AI提示词（必须传入）
 * @param {Array} customFallbackComments - 自备评论数组（可选）
 * @returns {string} 生成的评论内容（保证必须返回一条评论）
 */
function generateComment(aiPrompt, customFallbackComments) {
    console.log("开始生成AI评论..."); // 输出开始日志
    console.log("AI提示词: " + (aiPrompt || "无提示词")); // 输出AI提示词

    try {
        // 特殊情况1: 处理没有AI提示词的情况
        if (!aiPrompt || aiPrompt.trim() === "") {
            console.log("⚠️ 检测到没有AI提示词，使用自备评论"); // 输出警告日志
            return generateFallbackComment(customFallbackComments); // 调用备用评论生成函数，传入自备评论
        }

        // 使用传入的AI提示词
        var finalAiPrompt = aiPrompt.trim(); // 使用传入的AI提示词
        console.log("✓ 使用传入的AI提示词"); // 输出使用提示词的日志
        console.log("最终AI提示词: " + finalAiPrompt); // 输出最终使用的提示词

        // 构建API请求数据
        var requestData = {
            max_tokens: AI_CONFIG.maxTokens, // 最大令牌数
            top_k: AI_CONFIG.topK, // Top-K参数
            temperature: AI_CONFIG.temperature, // 温度参数
            messages: [
                {
                    role: "user", // 用户角色
                    content: finalAiPrompt // 使用最终确定的提示内容
                }
            ],
            model: AI_CONFIG.model, // AI模型
            stream: false // 不使用流式响应，直接获取完整结果
        };

        // 构建请求头
        var headers = {
            "Authorization": AI_CONFIG.token, // 授权令牌
            "Content-Type": "application/json" // 内容类型
        };

        console.log("正在发送API请求..."); // 输出请求日志

        // 发送HTTP POST请求
        var response = http.postJson(AI_CONFIG.url, requestData, {
            headers: headers, // 请求头
            timeout: 30000 // 30秒超时
        });

        console.log("API响应状态码: " + response.statusCode); // 输出响应状态码

        // 检查响应状态
        if (response.statusCode === 200) {
            var responseBody = response.body.json(); // 解析JSON响应
            console.log("API响应成功"); // 输出成功日志

            // 提取生成的评论内容
            if (responseBody && responseBody.choices && responseBody.choices.length > 0) {
                var generatedComment = responseBody.choices[0].message.content.trim(); // 获取生成的评论并去除首尾空格
                if (generatedComment && generatedComment !== "") {
                    console.log("✓ AI生成的评论: " + generatedComment); // 输出生成的评论
                    return generatedComment; // 返回AI评论内容
                }
            }

            // API响应格式异常，使用自备评论
            console.log("⚠️ API响应格式异常，使用自备评论"); // 输出警告日志
            return generateFallbackComment(customFallbackComments); // 返回自备评论

        } else {
            // API请求失败，使用自备评论
            console.error("✗ API请求失败，状态码: " + response.statusCode); // 输出错误状态码
            console.log("⚠️ 由于API请求失败，使用自备评论"); // 输出警告日志
            return generateFallbackComment(customFallbackComments); // 返回自备评论
        }

    } catch (error) {
        // 网络异常或其他错误，使用自备评论
        console.error("✗ 生成评论时发生错误: " + error); // 输出错误信息
        console.log("⚠️ 由于网络异常或其他错误，使用自备评论"); // 输出警告日志
        return generateFallbackComment(customFallbackComments); // 返回自备评论
    }
}

/**
 * 测试AI评论生成功能
 */
function testGenerateComment() {
    console.log("=== 开始测试AI评论生成功能 ==="); // 输出测试开始信息

    // 测试1: 使用AI提示词生成评论
    console.log("\n📝 测试1: 使用AI提示词生成评论"); // 输出测试类型
    var aiPrompt1 = "你是一个抖音用户，正在观看一个关于青丝白发的视频，请生成一条感人的评论，要求20字以内，带表情符号"; // AI提示词
    var normalComment = generateComment(aiPrompt1); // 生成评论
    console.log("结果: " + normalComment); // 输出结果

    // 测试2: 空提示词（使用自备评论）
    console.log("\n📝 测试2: 空提示词，使用默认自备评论"); // 输出测试类型
    var emptyComment = generateComment(""); // 传入空字符串
    console.log("结果: " + emptyComment); // 输出结果

    // 测试3: null提示词（使用自备评论）
    console.log("\n📝 测试3: null提示词，使用默认自备评论"); // 输出测试类型
    var nullComment = generateComment(null); // 传入null
    console.log("结果: " + nullComment); // 输出结果

    // 测试4: 使用自定义自备评论
    console.log("\n📝 测试4: 空提示词，使用自定义自备评论"); // 输出测试类型
    var customComments = [
        "这个视频超级棒！🌟", // 自定义评论1
        "看了好多遍了 💕", // 自定义评论2
        "太喜欢这种风格了 🎨", // 自定义评论3
        "简直是神作！⭐", // 自定义评论4
        "收藏了收藏了 📌" // 自定义评论5
    ];
    var customComment = generateComment("", customComments); // 空提示词，使用自定义自备评论
    console.log("结果: " + customComment); // 输出结果

    // 测试5: 完整参数测试（AI提示词 + 自备评论）
    console.log("\n📝 测试5: 完整参数测试"); // 输出测试类型
    var aiPrompt2 = "请生成一条关于美食的抖音评论，要表达强烈的食欲，简短有趣"; // AI提示词
    var fullParamsComment = generateComment(aiPrompt2, customComments); // AI提示词 + 自备评论
    console.log("结果: " + fullParamsComment); // 输出结果

    // 测试6: 多次调用自备评论，验证随机性
    console.log("\n📝 测试6: 多次调用自备评论，验证随机性"); // 输出测试类型
    for (var i = 0; i < 3; i++) {
        var randomComment = generateComment("", customComments); // 使用自定义自备评论
        console.log("第 " + (i + 1) + " 次: " + randomComment); // 输出每次结果
    }

    // 测试7: 不同类型的AI提示词
    console.log("\n📝 测试7: 不同类型的AI提示词"); // 输出测试类型
    var prompts = [
        "请生成一条幽默搞笑的抖音评论，要有趣味性", // 幽默类型
        "请生成一条温暖感人的抖音评论，要有情感共鸣", // 感人类型
        "请生成一条简洁有力的抖音评论，要一针见血" // 简洁类型
    ];

    for (var j = 0; j < prompts.length; j++) {
        var typeComment = generateComment(prompts[j], customComments); // 使用不同类型的提示词
        console.log("类型 " + (j + 1) + ": " + typeComment); // 输出结果
    }

    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
    console.log("✅ 所有情况都能正确返回评论，功能可靠"); // 输出总结信息
    console.log("✅ AI提示词优先，失败时自动使用自备评论"); // 输出功能总结
    console.log("✅ 支持外部传入自定义自备评论模板"); // 输出功能总结
}

// 导出模块，供其他脚本调用
module.exports = {
    generateComment: generateComment, // 导出评论生成函数（唯一对外接口）
    generateFallbackComment: generateFallbackComment, // 导出备用评论生成函数
    testGenerateComment: testGenerateComment, // 导出测试函数
    AI_CONFIG: AI_CONFIG, // 导出配置对象，允许外部修改
    FALLBACK_COMMENTS: FALLBACK_COMMENTS // 导出备用评论模板，允许外部自定义
};

// 如果直接运行此脚本，则执行测试
// if (typeof module === 'undefined' || require.main === module) {
//     console.log("直接运行ai.js，开始执行测试..."); // 输出直接运行提示
//     testGenerateComment(); // 执行测试函数
// }
