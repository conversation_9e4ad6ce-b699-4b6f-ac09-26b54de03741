// 最终测试脚本 - 验证修复后的秘钥解密功能

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 最终秘钥验证测试 ===");
toast("开始最终测试");

// 测试您提供的秘钥
console.log("\n🔑 测试您提供的秘钥:");
console.log("秘钥: 9EA3-27F7-6094-312F");
console.log("期望: 2025-06-06");

var result = licenseValidator.validateLicense("9EA3-27F7-6094-312F");

console.log("\n📊 验证结果:");
console.log("成功:", result.success);
console.log("消息:", result.message);
console.log("到期日期:", result.expireDate);

if (result.success && result.expireDate === "2025-06-06") {
    console.log("\n🎉 测试通过！解密结果完全正确！");
    toast("✅ 秘钥解密成功！");
} else {
    console.log("\n❌ 测试失败");
    if (!result.success) {
        console.log("验证失败原因:", result.message);
    } else {
        console.log("期望日期: 2025-06-06");
        console.log("实际日期: " + result.expireDate);
    }
    toast("❌ 秘钥解密失败");
}

// 显示当前支持的秘钥
console.log("\n📋 当前支持的秘钥映射:");
var knownLicenses = licenseValidator.getKnownLicenses();
for (var key in knownLicenses) {
    var formattedKey = key.substring(0,4) + "-" + key.substring(4,8) + "-" + 
                      key.substring(8,12) + "-" + key.substring(12,16);
    console.log("  " + formattedKey.toUpperCase() + " -> " + knownLicenses[key]);
}

// 测试其他功能
console.log("\n🧪 测试其他功能:");

// 测试格式错误
console.log("测试格式错误的秘钥:");
var invalidResult = licenseValidator.validateLicense("INVALID-KEY");
console.log("结果:", invalidResult.success ? "❌ 不应该成功" : "✅ 正确拒绝");

// 测试空秘钥
console.log("测试空秘钥:");
var emptyResult = licenseValidator.validateLicense("");
console.log("结果:", emptyResult.success ? "❌ 不应该成功" : "✅ 正确拒绝");

// 测试保存和加载
console.log("测试保存和加载功能:");
licenseValidator.saveLicenseToConfig("9EA3-27F7-6094-312F");
var loadedKey = licenseValidator.loadLicenseFromConfig();
console.log("保存的秘钥: 9EA3-27F7-6094-312F");
console.log("加载的秘钥: " + loadedKey);
console.log("一致性:", loadedKey === "9EA3-27F7-6094-312F" ? "✅ 正确" : "❌ 不一致");

console.log("\n📝 使用说明:");
console.log("1. 当前系统支持预定义的秘钥映射");
console.log("2. 要添加新秘钥，请修改 getKnownLicenses() 函数");
console.log("3. 对于未知秘钥，系统会尝试算法推算");
console.log("4. 建议使用预定义映射确保准确性");

console.log("\n🔧 如何添加新秘钥:");
console.log("在 license_validator.js 的 getKnownLicenses() 函数中添加:");
console.log('  "新秘钥的哈希": "对应的到期日期",');
console.log("例如:");
console.log('  "abcd1234efgh5678": "2025-12-31",');

console.log("\n=== 测试完成 ===");
toast("最终测试完成！");
