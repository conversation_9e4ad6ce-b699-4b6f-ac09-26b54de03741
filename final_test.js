// 测试清理后的秘钥验证功能

var licenseValidator = require("./license_validator.js");

console.log("=== 秘钥验证测试 ===");
toast("开始测试");

// 测试目标秘钥
console.log("测试秘钥: 7DCE-36A7-62EB-24F5");
console.log("期望结果: 2025-06-06");

var startTime = new Date().getTime();
var result = licenseValidator.validateLicense("7DCE-36A7-62EB-24F5");
var endTime = new Date().getTime();
var duration = (endTime - startTime) / 1000;

console.log("验证结果:", JSON.stringify(result, null, 2));
console.log("解密耗时:", duration + " 秒");

if (result.success && result.expireDate === "2025-06-06") {
    console.log("🎉 测试成功！");
    toast("✅ 解密成功: " + result.expireDate);
} else {
    console.log("❌ 测试失败");
    console.log("期望: 2025-06-06");
    console.log("实际: " + (result.expireDate || "null"));
    toast("❌ 解密失败");
}

console.log("=== 测试完成 ===");
