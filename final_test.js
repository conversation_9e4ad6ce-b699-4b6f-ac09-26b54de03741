// 暴力破解解密测试脚本

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 暴力破解解密测试 ===");
toast("开始暴力破解解密测试");

// 测试您提供的新秘钥
console.log("\n🔑 测试新的授权秘钥:");
console.log("秘钥: 7DCE-36A7-62EB-24F5");
console.log("使用暴力破解方法解密...");

var startTime = new Date().getTime();
var result = licenseValidator.validateLicense("7DCE-36A7-62EB-24F5");
var endTime = new Date().getTime();
var duration = (endTime - startTime) / 1000;

console.log("\n📊 验证结果:");
console.log("成功:", result.success);
console.log("消息:", result.message);
console.log("到期日期:", result.expireDate);
console.log("解密耗时:", duration + " 秒");

if (result.success) {
    console.log("\n🎉 解密成功！");
    console.log("找到的到期日期: " + result.expireDate);
    toast("✅ 秘钥解密成功！到期日期: " + result.expireDate);
} else {
    console.log("\n❌ 解密失败");
    console.log("失败原因:", result.message);
    toast("❌ 秘钥解密失败");
}

// 测试其他功能
console.log("\n🧪 测试其他功能:");

// 测试格式错误
console.log("测试格式错误的秘钥:");
var invalidResult = licenseValidator.validateLicense("INVALID-KEY");
console.log("结果:", invalidResult.success ? "❌ 不应该成功" : "✅ 正确拒绝");

// 测试空秘钥
console.log("测试空秘钥:");
var emptyResult = licenseValidator.validateLicense("");
console.log("结果:", emptyResult.success ? "❌ 不应该成功" : "✅ 正确拒绝");

// 测试保存和加载
console.log("测试保存和加载功能:");
licenseValidator.saveLicenseToConfig("7DCE-36A7-62EB-24F5");
var loadedKey = licenseValidator.loadLicenseFromConfig();
console.log("保存的秘钥: 7DCE-36A7-62EB-24F5");
console.log("加载的秘钥: " + loadedKey);
console.log("一致性:", loadedKey === "7DCE-36A7-62EB-24F5" ? "✅ 正确" : "❌ 不一致");

console.log("\n📝 解密原理:");
console.log("1. 遍历所有可能的日期 (2020-2030)");
console.log("2. 将日期与密钥组合: YYYYMMDD + '黑猫AI-secret-key-123'");
console.log("3. 计算MD5哈希值");
console.log("4. 比较哈希的前16位与秘钥是否匹配");
console.log("5. 找到匹配的日期即为到期日期");

console.log("\n⚡ 性能说明:");
console.log("- 最坏情况需要遍历约4000个日期");
console.log("- 每个日期需要计算一次MD5哈希");
console.log("- 通常在几秒内完成解密");

console.log("\n=== 测试完成 ===");
toast("暴力破解解密测试完成！");
