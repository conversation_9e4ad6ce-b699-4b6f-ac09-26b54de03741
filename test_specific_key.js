// 专门测试 7DCE-36A7-62EB-24F5 -> 2025-06-06 的脚本

var licenseValidator = require("./license_validator.js");

console.log("=== 测试特定秘钥解密 ===");
console.log("目标秘钥: 7DCE-36A7-62EB-24F5");
console.log("期望结果: 2025-06-06");

// 首先验证我们的MD5实现是否正确
console.log("\n=== 验证MD5实现 ===");
var knownTests = [
    {input: "hello", expected: "5d41402abc4b2a76b9719d911017c592"},
    {input: "world", expected: "7d793037a0760186574b0282f2f435e7"}
];

var md5Works = true;
for (var i = 0; i < knownTests.length; i++) {
    var test = knownTests[i];
    var result = licenseValidator.md5(test.input);
    var correct = result === test.expected;
    console.log("MD5('" + test.input + "'): " + (correct ? "✅" : "❌"));
    if (!correct) {
        console.log("  期望: " + test.expected);
        console.log("  实际: " + result);
        md5Works = false;
    }
}

if (!md5Works) {
    console.log("❌ MD5实现有问题，无法继续测试");
    return;
}

// 验证目标组合的MD5
console.log("\n=== 验证目标组合 ===");
var targetDate = "20250606";
var secretKey = "黑猫AI-secret-key-123";
var mixedData = targetDate + secretKey;
var targetHash = "7dce36a762eb24f5";

console.log("日期: " + targetDate);
console.log("密钥: " + secretKey);
console.log("组合: " + mixedData);

var calculatedHash = licenseValidator.md5(mixedData);
console.log("计算的MD5: " + calculatedHash);
console.log("前16位: " + calculatedHash.substring(0, 16));
console.log("目标前16位: " + targetHash);

var hashMatches = calculatedHash.substring(0, 16) === targetHash;
console.log("哈希匹配: " + (hashMatches ? "✅" : "❌"));

if (!hashMatches) {
    console.log("❌ 哈希不匹配！这意味着:");
    console.log("1. MD5实现可能有问题");
    console.log("2. 或者加密逻辑不正确");
    console.log("3. 或者提供的期望结果不正确");
    
    // 让我们尝试一些变体
    console.log("\n=== 尝试其他可能的组合 ===");
    var variants = [
        "2025-06-06" + secretKey,
        "2025/06/06" + secretKey,
        "06/06/2025" + secretKey,
        targetDate + secretKey.toUpperCase(),
        targetDate + secretKey.toLowerCase()
    ];
    
    for (var j = 0; j < variants.length; j++) {
        var variant = variants[j];
        var variantHash = licenseValidator.md5(variant);
        var variantMatch = variantHash.substring(0, 16) === targetHash;
        console.log("尝试: " + variant);
        console.log("  哈希: " + variantHash.substring(0, 16));
        console.log("  匹配: " + (variantMatch ? "✅" : "❌"));
        if (variantMatch) {
            console.log("🎉 找到正确的组合！");
            break;
        }
    }
} else {
    console.log("✅ 哈希匹配正确！");
}

// 执行完整的解密测试
console.log("\n=== 执行完整解密 ===");
var startTime = new Date().getTime();
var result = licenseValidator.validateLicense("7DCE-36A7-62EB-24F5");
var endTime = new Date().getTime();
var duration = (endTime - startTime) / 1000;

console.log("解密耗时: " + duration + " 秒");
console.log("解密结果: " + JSON.stringify(result, null, 2));

if (result.success && result.expireDate === "2025-06-06") {
    console.log("🎉 解密完全成功！");
} else {
    console.log("❌ 解密失败");
    console.log("期望: 2025-06-06");
    console.log("实际: " + (result.expireDate || "null"));
    
    if (!result.success) {
        console.log("失败原因: " + result.message);
    }
}
