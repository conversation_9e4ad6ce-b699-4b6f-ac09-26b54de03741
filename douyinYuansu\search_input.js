/**
 * 抖音搜索输入模块
 * 根据控件属性定位并模拟真人输入搜索内容
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找搜索输入框的主函数
 * @returns {UiObject|null} 找到的搜索输入框控件，未找到返回null
 */
function findSearchInput() {
    console.log("开始查找搜索输入框..."); // 输出开始查找日志
    launchApp("抖音");
    try {
        // 方法1: 通过完整的资源ID查找（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var inputByFullId = id("com.ss.android.ugc.aweme:id/et_search_kw").visibleToUser().findOne(3000); // 使用完整的资源ID，确保可见，等待3秒
        if (inputByFullId) {
            console.log("✓ 通过完整资源ID找到搜索输入框"); // 输出找到输入框的日志
            return inputByFullId; // 返回找到的输入框
        }

        // 方法2: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var inputByShortId = id("et_search_kw").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (inputByShortId) {
            console.log("✓ 通过简短ID找到搜索输入框"); // 输出找到输入框的日志
            return inputByShortId; // 返回找到的输入框
        }

        // 方法3: 通过属性组合查找
        console.log("方法3: 尝试通过属性组合查找..."); // 输出当前尝试的方法
        var inputByProps = editable(true) // 可编辑
            .enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .longClickable(true) // 可长按
            .depth(2) // 深度为2
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .visibleToUser() // 对用户可见
            .findOne(2000); // 等待2秒

        if (inputByProps) {
            console.log("✓ 通过属性组合找到搜索输入框"); // 输出找到输入框的日志
            return inputByProps; // 返回找到的输入框
        }

        console.log("✗ 所有方法都未找到搜索输入框"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 查找搜索输入框时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人输入文本
 * @param {UiObject} input - 要输入的输入框控件
 * @param {string} text - 要输入的文本内容
 * @returns {boolean} 输入是否成功
 */
function humanLikeInput(input, text) {
    console.log("开始模拟真人输入操作..."); // 输出开始输入日志

    try {
        // 先点击输入框获取焦点
        var bounds = input.bounds(); // 获取输入框边界
        if (!bounds) {
            console.error("✗ 无法获取输入框位置信息"); // 输出错误信息
            return false; // 返回失败
        }

        // 计算输入框中心点坐标
        var centerX = bounds.centerX(); // 输入框中心X坐标
        var centerY = bounds.centerY(); // 输入框中心Y坐标

        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-10, 10); // X轴随机偏移-10到10像素
        var randomOffsetY = random(-5, 5); // Y轴随机偏移-5到5像素

        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标

        // 模拟真人的点击前停顿
        var preClickDelay = random(100, 300); // 点击前随机延迟100-300毫秒
        sleep(preClickDelay); // 执行延迟

        // 执行点击操作
        press(clickX, clickY, random(50, 150)); // 执行坐标点击，随机按压时长
        sleep(random(200, 500)); // 点击后随机延迟

        // 清除现有文本
        input.setText(""); // 清空输入框
        sleep(random(200, 400)); // 随机延迟
        input.setText(text);
        // 模拟真人输入文本（逐字输入并添加随机延迟）
        // for (var i = 0; i < text.length; i++) {
        //     var char = text.charAt(i); // 获取当前字符
        //     input.setText(input.text() + char); // 添加一个字符
        //     sleep(random(50, 150)); // 每个字符之间添加随机延迟
        // }

        console.log("✓ 成功输入文本: " + text); // 输出成功日志
        return true; // 返回成功

    } catch (error) {
        console.error("✗ 模拟输入时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找搜索框并输入内容
 * @param {string} searchText - 要搜索的文本内容
 * @returns {boolean} 操作是否成功
 */
function searchInput(searchText) {
    console.log("=== 开始执行搜索输入操作 ==="); // 输出开始操作日志

    // 检查是否在抖音应用中
    if (!currentPackage().includes("aweme")) {
        console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
        launchApp("抖音"); // 启动抖音应用
        sleep(3000); // 等待3秒让应用加载
        waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    }

    // 查找搜索输入框
    var searchInput = findSearchInput(); // 调用查找函数

    if (!searchInput) {
        console.error("✗ 未找到搜索输入框，操作失败"); // 输出失败信息
        toast("未找到搜索输入框"); // 显示失败提示
        return false; // 返回失败
    }

    console.log("✓ 成功找到搜索输入框"); // 输出成功找到日志
    toast("找到搜索输入框，准备输入"); // 显示找到提示

    // 执行模拟真人输入
    var inputResult = humanLikeInput(searchInput, searchText); // 调用输入函数

    if (inputResult) {
        console.log("✓ 搜索内容输入成功"); // 输出成功日志
        toast("✓ 搜索内容输入成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 搜索内容输入失败"); // 输出失败日志
        toast("✗ 搜索内容输入失败"); // 显示失败提示
        return false; // 返回失败
    }
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

/**
 * 测试函数：测试搜索输入功能
 */
function testSearchInput() {
    console.log("=== 开始测试搜索输入功能 ==="); // 输出测试开始信息
    // 测试完整流程
    console.log("\n📝 测试2: 完整输入流程"); // 输出测试类型
    var result = searchInput("我们都是好好孩子"); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果

    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}
// testSearchInput();
// 导出模块，供其他脚本调用
module.exports = {
    findSearchInput: findSearchInput, // 导出查找搜索输入框函数
    humanLikeInput: humanLikeInput, // 导出模拟真人输入函数
    searchInput: searchInput, // 导出主函数
    testSearchInput: testSearchInput // 导出测试函数
};

