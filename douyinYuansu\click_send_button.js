/**
 * 点击抖音发送按钮模块
 * 根据控件属性定位并模拟真人点击发送按钮
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找发送按钮的主函数
 * @returns {UiObject|null} 找到的发送按钮控件，未找到返回null
 */
function findSendButton() {
    console.log("开始查找发送按钮..."); // 输出开始查找日志
    
    try {
        // 方法1: 通过完整的资源ID查找（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var buttonByFullId = id("com.ss.android.ugc.aweme:id/d2h").visibleToUser().findOne(3000); // 使用完整的资源ID，确保可见，等待3秒
        if (buttonByFullId) {
            console.log("✓ 通过完整资源ID找到发送按钮"); // 输出找到按钮的日志
            return buttonByFullId; // 返回找到的按钮
        }
        
        // 方法2: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var buttonByShortId = id("d2h").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (buttonByShortId) {
            console.log("✓ 通过简短ID找到发送按钮"); // 输出找到按钮的日志
            return buttonByShortId; // 返回找到的按钮
        }
        
        // 方法3: 通过text内容查找
        console.log("方法3: 尝试通过text内容查找..."); // 输出当前尝试的方法
        var buttonByText = text("发送").visibleToUser().findOne(2000); // 通过text查找
        if (buttonByText) {
            console.log("✓ 通过text内容找到发送按钮"); // 输出找到按钮的日志
            return buttonByText; // 返回找到的按钮
        }
        
        // 方法4: 通过text关键词查找
        console.log("方法4: 尝试通过text关键词查找..."); // 输出当前尝试的方法
        var buttonByTextContains = textContains("发送").visibleToUser().findOne(2000); // 查找text包含"发送"的控件
        if (buttonByTextContains) {
            console.log("✓ 通过text关键词找到发送按钮，text: " + buttonByTextContains.text()); // 输出找到按钮的日志
            return buttonByTextContains; // 返回找到的按钮
        }
        
        // 方法5: 通过属性组合查找
        console.log("方法5: 尝试通过属性组合查找..."); // 输出当前尝试的方法
        var buttonByProps = enabled(true) // 启用状态
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒
            
        if (buttonByProps) {
            // 进一步验证text内容或ID
            var propsText = buttonByProps.text(); // 获取控件文本
            var propsId = buttonByProps.id(); // 获取控件ID
            if ((propsText && propsText.includes("发送")) || 
                (propsId && propsId.includes("d2h"))) {
                console.log("✓ 通过属性组合找到发送按钮，text: " + propsText + ", id: " + propsId); // 输出找到按钮的日志
                return buttonByProps; // 返回找到的按钮
            }
        }
        
        // 方法6: 通过多重属性精确匹配查找
        console.log("方法6: 尝试通过多重属性精确匹配查找..."); // 输出当前尝试的方法
        var buttonByMultiProps = enabled(true) // 启用状态
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒
            
        if (buttonByMultiProps) {
            // 进一步验证是否为发送按钮
            var multiPropsText = buttonByMultiProps.text(); // 获取控件文本
            var multiPropsId = buttonByMultiProps.id(); // 获取控件ID
            if ((multiPropsText && multiPropsText === "发送") || 
                (multiPropsId && multiPropsId.includes("d2h"))) {
                console.log("✓ 通过多重属性精确匹配找到发送按钮，text: " + multiPropsText); // 输出找到按钮的日志
                return buttonByMultiProps; // 返回找到的按钮
            }
        }
        
        // 方法7: 遍历所有可见控件查找
        console.log("方法7: 遍历所有可见控件查找..."); // 输出当前尝试的方法
        var allVisibleViews = enabled(true).visibleToUser().find(); // 查找所有可见的启用控件
        console.log("找到 " + allVisibleViews.length + " 个可见的启用控件"); // 输出找到的控件数量
        
        for (var i = 0; i < allVisibleViews.length; i++) {
            var element = allVisibleViews[i]; // 遍历每个控件
            var elementId = element.id(); // 获取控件ID
            var elementText = element.text(); // 获取控件文本
            var elementPackage = element.packageName(); // 获取包名
            
            // 检查是否符合发送按钮的特征
            if (elementPackage && elementPackage.includes("aweme") && 
                ((elementId && elementId.includes("d2h")) || 
                 (elementText && elementText === "发送"))) {
                console.log("✓ 通过遍历找到发送按钮 - ID: " + elementId + ", text: " + elementText); // 输出找到按钮的日志
                return element; // 返回找到的按钮
            }
        }
        
        console.log("✗ 所有方法都未找到发送按钮"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到
        
    } catch (error) {
        console.error("✗ 查找发送按钮时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} button - 要点击的按钮控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(button) {
    console.log("开始模拟真人点击发送按钮操作..."); // 输出开始点击日志
    
    try {
        // 获取按钮的位置信息
        var bounds = button.bounds(); // 获取按钮边界
        if (!bounds) {
            console.error("✗ 无法获取按钮位置信息"); // 输出错误信息
            return false; // 返回失败
        }
        
        console.log("按钮位置: " + bounds.toString()); // 输出按钮位置
        
        // 计算按钮中心点坐标
        var centerX = bounds.centerX(); // 按钮中心X坐标
        var centerY = bounds.centerY(); // 按钮中心Y坐标
        
        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-8, 8); // X轴随机偏移-8到8像素
        var randomOffsetY = random(-5, 5); // Y轴随机偏移-5到5像素
        
        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标
        
        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标
        
        // 模拟真人的点击前停顿
        var preClickDelay = random(150, 400); // 点击前随机延迟150-400毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟
        
        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = press(clickX, clickY, random(80, 200)); // 执行坐标点击，随机按压时长
        
        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = button.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }
        
        // 模拟真人的点击后停顿
        var postClickDelay = random(300, 600); // 点击后随机延迟300-600毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟
        
        console.log("✓ 模拟真人点击发送按钮操作完成"); // 输出完成日志
        return true; // 返回成功
        
    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击发送按钮
 * @returns {boolean} 操作是否成功
 */
function clickSendButton() {
    console.log("=== 开始执行点击发送按钮操作 ==="); // 输出开始操作日志
    
    // 检查是否在抖音应用中
    // if (!currentPackage().includes("aweme")) {
    //     console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
    //     launchApp("抖音"); // 启动抖音应用
    //     sleep(3000); // 等待3秒让应用加载
    //     waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    // }
    
    // 查找发送按钮
    var sendButton = findSendButton(); // 调用查找函数
    
    if (!sendButton) {
        console.error("✗ 未找到发送按钮，操作失败"); // 输出失败信息
        toast("未找到发送按钮"); // 显示失败提示
        return false; // 返回失败
    }
    
    console.log("✓ 成功找到发送按钮"); // 输出成功找到日志
    toast("找到发送按钮，准备点击"); // 显示找到提示
    
    // 执行模拟真人点击
    var clickResult = humanLikeClick(sendButton); // 调用点击函数
    
    if (clickResult) {
        console.log("✓ 发送按钮点击成功"); // 输出成功日志
        toast("✓ 发送按钮点击成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 发送按钮点击失败"); // 输出失败日志
        toast("✗ 发送按钮点击失败"); // 显示失败提示
        return false; // 返回失败
    }
}

/**
 * 测试函数：测试查找和点击功能
 */
function testClickSendButton() {
    console.log("=== 开始测试点击发送按钮功能 ==="); // 输出测试开始信息
    
    // 测试查找功能
    console.log("\n📝 测试1: 查找发送按钮"); // 输出测试类型
    var button = findSendButton(); // 查找按钮
    if (button) {
        console.log("✓ 查找测试成功"); // 输出测试成功
        console.log("按钮信息: ID=" + button.id() + ", text=" + button.text()); // 输出按钮信息
        console.log("按钮详细属性:"); // 输出详细属性标题
        console.log("  - className: " + button.className()); // 输出类名
        console.log("  - editable: " + button.editable); // 输出可编辑状态
        console.log("  - enabled: " + button.enabled()); // 输出启用状态
        console.log("  - focusable: " + button.focusable()); // 输出可获得焦点状态
        console.log("  - longClickable: " + button.longClickable()); // 输出可长按状态
        console.log("  - visibleToUser: " + button.visibleToUser()); // 输出可见性状态
        console.log("  - bounds: " + button.bounds()); // 输出边界信息
    } else {
        console.log("✗ 查找测试失败"); // 输出测试失败
    }
    
    // 测试完整流程
    console.log("\n📝 测试2: 完整点击流程"); // 输出测试类型
    var result = clickSendButton(); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果
    
    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

// 导出模块，供其他脚本调用
module.exports = {
    findSendButton: findSendButton, // 导出查找发送按钮函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickSendButton: clickSendButton, // 导出主函数
    testClickSendButton: testClickSendButton // 导出测试函数
};

// 如果直接运行此脚本，则执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("直接运行click_send_button.js，开始执行测试..."); // 输出直接运行提示
    testClickSendButton(); // 执行测试函数
}
