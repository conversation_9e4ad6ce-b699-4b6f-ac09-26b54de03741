/**
 * 简洁的搜索按钮查找和点击函数
 * 优先使用desc和text搜索，代码简练
 */

/**
 * 查找搜索按钮
 * @returns {UiObject|null} 找到的搜索按钮，未找到返回null
 */
function findSearchButton() {
    console.log("查找搜索按钮..."); // 开始查找日志
    
    try {
        // 方法1: 通过desc查找
        var button = desc("搜索").visibleToUser().findOne(2000); // 通过desc查找可见按钮
        if (button) {
            console.log("✓ 通过desc找到搜索按钮"); // 找到按钮日志
            return button; // 返回按钮
        }
        
        // 方法2: 通过text查找
        button = text("搜索").visibleToUser().findOne(2000); // 通过text查找可见按钮
        if (button) {
            console.log("✓ 通过text找到搜索按钮"); // 找到按钮日志
            return button; // 返回按钮
        }
        
        // 方法3: 通过desc关键词查找
        button = descContains("搜索").visibleToUser().findOne(2000); // 通过desc关键词查找
        if (button) {
            console.log("✓ 通过desc关键词找到搜索按钮"); // 找到按钮日志
            return button; // 返回按钮
        }
        
        console.log("✗ 未找到搜索按钮"); // 未找到日志
        return null; // 返回null
        
    } catch (error) {
        console.error("✗ 查找搜索按钮出错: " + error); // 错误日志
        return null; // 返回null
    }
}

/**
 * 点击搜索按钮
 * @param {UiObject} button 要点击的按钮对象
 * @returns {boolean} 点击是否成功
 */
function clickSearchButton(button) {
    console.log("点击搜索按钮..."); // 开始点击日志
    
    try {
        // 获取按钮位置
        var bounds = button.bounds(); // 获取按钮边界
        var centerX = bounds.centerX(); // 中心X坐标
        var centerY = bounds.centerY(); // 中心Y坐标
        
        // 添加随机偏移模拟真人点击
        var offsetX = random(-3, 3); // X轴随机偏移
        var offsetY = random(-3, 3); // Y轴随机偏移
        var clickX = centerX + offsetX; // 最终X坐标
        var clickY = centerY + offsetY; // 最终Y坐标
        
        // 点击前延迟
        sleep(random(100, 200)); // 随机延迟100-200毫秒
        
        // 执行点击
        var success = click(clickX, clickY); // 坐标点击
        if (!success) {
            success = button.click(); // 备用控件点击
        }
        
        if (success) {
            console.log("✓ 搜索按钮点击成功"); // 成功日志
            sleep(random(200, 400)); // 点击后延迟
            return true; // 返回成功
        } else {
            console.error("✗ 搜索按钮点击失败"); // 失败日志
            return false; // 返回失败
        }
        
    } catch (error) {
        console.error("✗ 点击搜索按钮出错: " + error); // 错误日志
        return false; // 返回失败
    }
}

/**
 * 查找并点击搜索按钮的主函数
 * @returns {boolean} 操作是否成功
 */
function findAndClickSearch() {
    console.log("开始查找并点击搜索按钮..."); // 开始操作日志
    
    var button = findSearchButton(); // 查找按钮
    if (!button) {
        console.error("✗ 未找到搜索按钮，操作失败"); // 未找到按钮日志
        return false; // 返回失败
    }
    
    var success = clickSearchButton(button); // 点击按钮
    if (success) {
        console.log("✓ 搜索按钮操作完成"); // 操作完成日志
    } else {
        console.error("✗ 搜索按钮操作失败"); // 操作失败日志
    }
    
    return success; // 返回操作结果
}
// findAndClickSearch();
// 导出函数供其他模块使用
module.exports = {
    findSearchButton: findSearchButton, // 导出查找函数
    clickSearchButton: clickSearchButton, // 导出点击函数
    findAndClickSearch: findAndClickSearch // 导出主函数
};

// 如果直接运行此文件，执行测试
// if (require.main === module) {
//     console.log("=== 搜索按钮测试 ==="); // 测试开始日志
//     findAndClickSearch(); // 执行测试
// }
