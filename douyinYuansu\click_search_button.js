/** 选择点击搜索按钮 */
function findSearchButton() {
    console.log("开始查找搜索按钮..."); // 输出搜索按钮查找日志
    try {
        // 方法1: 通过desc内容查找
        console.log("方法3: 尝试通过desc内容查找..."); // 输出当前尝试的方法
        var buttonByDesc = desc("搜索").visibleToUser().findOne(2000); // 通过desc查找
        if (buttonByDesc) {
            console.log("✓ 通过desc内容找到搜索按钮"); // 输出找到按钮的日志
            return buttonByDesc; // 返回找到的按钮
        }
        // 方法2: 通过desc关键词查找
        console.log("方法4: 尝试通过desc关键词查找..."); // 输出当前尝试的方法
        var buttonByDescContains = descContains("搜索").visibleToUser().findOne(2000); // 查找desc包含"搜索"的控件
        if (buttonByDescContains) {
            console.log("✓ 通过desc关键词找到搜索按钮，desc: " + buttonByDescContains.desc()); // 输出找到按钮的日志
            return buttonByDescContains; // 返回找到的按钮
        }
        // 方法3: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var buttonByShortId = id("zv=").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (buttonByShortId) {
            console.log("✓ 通过简短ID找到搜索按钮"); // 输出找到按钮的日志
            return buttonByShortId; // 返回找到的按钮
        }
        // 方法4: 通过简短id查找
        var buttonByFullId = id("com.ss.android.ugc.aweme:id/zv=").visibleToUser().findOne(3000); // 使用完整的资源ID查找，确保可见，等待3秒
        if (buttonByFullId) {
            console.log("✓ 通过完整资源ID找到搜索按钮"); // 输出找到按钮的日志
            return buttonByFullId; // 返回找到的按钮
        }

        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 查找搜索按钮时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} button - 要点击的按钮控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(button) {
    console.log("开始模拟真人点击搜索按钮操作..."); // 输出开始点击日志
    
    try {
        // 获取按钮的位置信息
        var bounds = button.bounds(); // 获取按钮边界
        if (!bounds) {
            console.error("✗ 无法获取按钮位置信息"); // 输出错误信息
            return false; // 返回失败
        }
        
        console.log("按钮位置: " + bounds.toString()); // 输出按钮位置
        
        // 计算按钮中心点坐标
        var centerX = bounds.centerX(); // 按钮中心X坐标
        var centerY = bounds.centerY(); // 按钮中心Y坐标
        
        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-6, 6); // X轴随机偏移-6到6像素
        var randomOffsetY = random(-4, 4); // Y轴随机偏移-4到4像素
        
        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标
        
        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标
        
        // 模拟真人的点击前停顿
        var preClickDelay = random(120, 350); // 点击前随机延迟120-350毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟
        
        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = press(clickX, clickY, random(60, 180)); // 执行坐标点击，随机按压时长
        
        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = button.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }
        
        // 模拟真人的点击后停顿
        var postClickDelay = random(250, 500); // 点击后随机延迟250-500毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟
        
        console.log("✓ 模拟真人点击搜索按钮操作完成"); // 输出完成日志
        return true; // 返回成功
        
    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击搜索按钮
 * @returns {boolean} 操作是否成功
 */
function clickSearchButton() {
    console.log("=== 开始执行点击搜索按钮操作 ==="); // 输出开始操作日志
    
    // 检查是否在抖音应用中
    if (!currentPackage().includes("aweme")) {
        console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
        launchApp("抖音"); // 启动抖音应用
        sleep(3000); // 等待3秒让应用加载
        waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    }
    
    // 查找搜索按钮
    var closeButton = findSearchButton(); // 调用查找函数
    
    if (!closeButton) {
        console.error("✗ 未找到搜索按钮，操作失败"); // 输出失败信息
        toast("未找到搜索按钮"); // 显示失败提示
        return false; // 返回失败
    }
    
    console.log("✓ 成功找到搜索按钮"); // 输出成功找到日志
    toast("找到搜索按钮，准备点击"); // 显示找到提示
    
    // 执行模拟真人点击
    var clickResult = humanLikeClick(closeButton); // 调用点击函数
    
    if (clickResult) {
        console.log("✓ 搜索按钮点击成功"); // 输出成功日志
        toast("✓ 搜索按钮点击成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 搜索按钮点击失败"); // 输出失败日志
        toast("✗ 搜索按钮点击失败"); // 显示失败提示
        return false; // 返回失败
    }
}
// clickSearchButton()
// 导出模块，供其他脚本调用
module.exports = {
    findSearchButton: findSearchButton, // 导出查找搜素按钮函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickSearchButton: clickSearchButton //导出搜索按钮的搜索和点击
};