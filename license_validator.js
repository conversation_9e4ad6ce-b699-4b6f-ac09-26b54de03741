// 秘钥验证模块
// 用于验证用户输入的授权秘钥

// 定义配置文件路径
var LICENSE_CONFIG_FILE = files.path("./license_config.json"); // 秘钥配置文件路径

/**
 * 验证秘钥的主函数
 * @param {string} licenseKey - 用户输入的秘钥
 * @returns {object} 验证结果 {success: boolean, message: string, expireDate: string}
 */
function validateLicense(licenseKey) {
    console.log("开始验证秘钥: " + licenseKey);

    try {
        // 1. 检查秘钥是否为空
        if (!licenseKey || licenseKey.trim() === "") {
            return {
                success: false,
                message: "请输入授权秘钥",
                expireDate: null
            };
        }

        // 2. 保存秘钥到配置文件
        saveLicenseToConfig(licenseKey.trim());

        // 3. 解密秘钥获取到期日期
        var expireDate = decryptLicense(licenseKey.trim());
        if (!expireDate) {
            return {
                success: false,
                message: "秘钥无效或无法解密，请检查秘钥是否正确",
                expireDate: null
            };
        }

        // 4. 验证到期日期
        var currentDate = new Date();
        var expireDateObj = new Date(expireDate);

        console.log("当前日期: " + currentDate.toISOString());
        console.log("到期日期: " + expireDateObj.toISOString());

        if (expireDateObj <= currentDate) {
            return {
                success: false,
                message: "授权已过期，到期日期: " + expireDate,
                expireDate: expireDate
            };
        }

        // 5. 验证成功
        var remainingDays = Math.ceil((expireDateObj - currentDate) / (1000 * 60 * 60 * 24));
        return {
            success: true,
            message: "授权验证成功，剩余 " + remainingDays + " 天",
            expireDate: expireDate
        };

    } catch (e) {
        console.error("验证秘钥时发生错误: " + e);
        return {
            success: false,
            message: "验证过程中发生错误: " + e.message,
            expireDate: null
        };
    }
}

/**
 * 解密秘钥获取到期日期
 * 使用暴力破解方法：遍历所有可能的日期，与密钥组合进行MD5哈希比较
 * @param {string} licenseKey - 格式化的秘钥 (XXXX-XXXX-XXXX-XXXX)
 * @returns {string|null} 到期日期字符串或null
 */
function decryptLicense(licenseKey) {
    try {
        console.log("开始解密秘钥: " + licenseKey);

        // 1. 验证秘钥格式 (XXXX-XXXX-XXXX-XXXX)
        var keyPattern = /^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/;
        if (!keyPattern.test(licenseKey)) {
            console.log("秘钥格式不正确");
            return null;
        }

        // 2. 移除连字符，获取16位哈希，转为小写
        var targetHash = licenseKey.replace(/-/g, '').toLowerCase();
        console.log("目标哈希: " + targetHash);

        // 3. 密钥字符串（与PHP代码中的密钥保持一致）
        var secretKey = "黑猫AI-secret-key-123";

        // 4. 暴力破解：遍历所有可能的日期 (2020-2030)
        console.log("开始暴力破解，遍历日期范围 2020-2030...");

        for (var year = 2020; year <= 2030; year++) {
            for (var month = 1; month <= 12; month++) {
                for (var day = 1; day <= 31; day++) {
                    // 跳过无效日期
                    if (!isValidDate(year, month, day)) {
                        continue;
                    }

                    // 构造测试日期字符串 (格式: YYYYMMDD)
                    var testDate = year.toString() +
                                  (month < 10 ? "0" + month : month.toString()) +
                                  (day < 10 ? "0" + day : day.toString());

                    // 组合数据：日期 + 密钥
                    var mixedData = testDate + secretKey;

                    // 计算MD5哈希
                    var hash = md5(mixedData);

                    // 比较哈希的前16位
                    if (hash.substring(0, 16) === targetHash) {
                        // 找到匹配的日期！
                        var expireDate = year + "-" +
                                        (month < 10 ? "0" + month : month.toString()) + "-" +
                                        (day < 10 ? "0" + day : day.toString());

                        console.log("🎉 找到匹配日期: " + expireDate);
                        console.log("测试数据: " + mixedData);
                        console.log("生成哈希: " + hash);
                        console.log("匹配部分: " + hash.substring(0, 16));

                        return expireDate;
                    }
                }

                // 每处理完一个月，输出进度
                if (month % 3 === 0) {
                    console.log("处理进度: " + year + "年" + month + "月");
                }
            }
        }

        console.log("❌ 未找到匹配的日期");
        return null;

    } catch (e) {
        console.error("解密秘钥失败: " + e);
        return null;
    }
}

/**
 * 保存秘钥到配置文件
 * @param {string} licenseKey - 秘钥
 */
function saveLicenseToConfig(licenseKey) {
    try {
        var config = {
            licenseKey: licenseKey,
            savedAt: new Date().toISOString()
        };

        var configJson = JSON.stringify(config);
        files.write(LICENSE_CONFIG_FILE, configJson);
        console.log("秘钥已保存到配置文件");

    } catch (e) {
        console.error("保存秘钥到配置文件失败: " + e);
    }
}

/**
 * 从配置文件读取秘钥
 * @returns {string|null} 保存的秘钥或null
 */
function loadLicenseFromConfig() {
    try {
        if (files.exists(LICENSE_CONFIG_FILE)) {
            var configStr = files.read(LICENSE_CONFIG_FILE);
            var config = JSON.parse(configStr);
            console.log("从配置文件加载秘钥成功");
            return config.licenseKey || null;
        }
        return null;
    } catch (e) {
        console.error("从配置文件读取秘钥失败: " + e);
        return null;
    }
}

/**
 * 验证日期是否有效（年月日组合）
 * @param {number} year - 年份
 * @param {number} month - 月份 (1-12)
 * @param {number} day - 日期 (1-31)
 * @returns {boolean} 是否有效
 */
function isValidDate(year, month, day) {
    // 基本范围检查
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // 每月天数检查
    var daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 闰年检查
    if (month === 2 && isLeapYear(year)) {
        daysInMonth[1] = 29;
    }

    return day <= daysInMonth[month - 1];
}

/**
 * 检查是否为闰年
 * @param {number} year - 年份
 * @returns {boolean} 是否为闰年
 */
function isLeapYear(year) {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * MD5哈希函数 - 使用AutoJS内置的crypto模块或简化实现
 * @param {string} str - 要哈希的字符串
 * @returns {string} MD5哈希值
 */
function md5(str) {
    // 尝试使用AutoJS的内置crypto模块
    try {
        if (typeof crypto !== 'undefined' && crypto.createHash) {
            return crypto.createHash('md5').update(str).digest('hex');
        }
    } catch (e) {
        console.log("crypto模块不可用，使用自定义MD5实现");
    }

    // 如果没有crypto模块，使用自定义实现
    // 这是一个经过验证的MD5实现
    function md5cycle(x, k) {
        var a = x[0], b = x[1], c = x[2], d = x[3];

        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);

        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);

        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);

        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);

        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    }

    function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32((a << s) | (a >>> (32 - s)), b);
    }

    function ff(a, b, c, d, x, s, t) {
        return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }

    function gg(a, b, c, d, x, s, t) {
        return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }

    function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    }

    function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }

    function md51(s) {
        var n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
        for (i = 64; i <= s.length; i += 64) {
            md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < s.length; i++)
            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
        tail[i >> 2] |= 0x80 << ((i % 4) << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i++) tail[i] = 0;
        }
        tail[14] = n * 8;
        md5cycle(state, tail);
        return state;
    }

    function md5blk(s) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = s.charCodeAt(i)
                + (s.charCodeAt(i + 1) << 8)
                + (s.charCodeAt(i + 2) << 16)
                + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
    }

    var hex_chr = '0123456789abcdef'.split('');

    function rhex(n) {
        var s = '', j = 0;
        for (; j < 4; j++)
            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
                + hex_chr[(n >> (j * 8)) & 0x0F];
        return s;
    }

    function hex(x) {
        for (var i = 0; i < x.length; i++)
            x[i] = rhex(x[i]);
        return x.join('');
    }

    function add32(a, b) {
        return (a + b) & 0xFFFFFFFF;
    }

    return hex(md51(str));
}

// 导出函数供其他模块使用
module.exports = {
    validateLicense: validateLicense,
    loadLicenseFromConfig: loadLicenseFromConfig,
    saveLicenseToConfig: saveLicenseToConfig,
    md5: md5  // 导出MD5函数用于测试
};
