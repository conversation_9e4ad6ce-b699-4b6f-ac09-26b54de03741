// 秘钥验证模块
// 用于验证用户输入的授权秘钥

// 定义配置文件路径
var LICENSE_CONFIG_FILE = files.path("./license_config.json"); // 秘钥配置文件路径

/**
 * 验证秘钥的主函数
 * @param {string} licenseKey - 用户输入的秘钥
 * @returns {object} 验证结果 {success: boolean, message: string, expireDate: string}
 */
function validateLicense(licenseKey) {
    console.log("开始验证秘钥: " + licenseKey);

    try {
        // 1. 检查秘钥是否为空
        if (!licenseKey || licenseKey.trim() === "") {
            return {
                success: false,
                message: "请输入授权秘钥",
                expireDate: null
            };
        }

        // 2. 保存秘钥到配置文件
        saveLicenseToConfig(licenseKey.trim());

        // 3. 解密秘钥获取到期日期
        var expireDate = decryptLicense(licenseKey.trim());
        if (!expireDate) {
            return {
                success: false,
                message: "秘钥无效或无法解密，请检查秘钥是否正确",
                expireDate: null
            };
        }

        // 4. 验证到期日期
        var currentDate = new Date();
        var expireDateObj = new Date(expireDate);

        console.log("当前日期: " + currentDate.toISOString());
        console.log("到期日期: " + expireDateObj.toISOString());

        if (expireDateObj <= currentDate) {
            return {
                success: false,
                message: "授权已过期，到期日期: " + expireDate,
                expireDate: expireDate
            };
        }

        // 5. 验证成功
        var remainingDays = Math.ceil((expireDateObj - currentDate) / (1000 * 60 * 60 * 24));
        return {
            success: true,
            message: "授权验证成功，剩余 " + remainingDays + " 天",
            expireDate: expireDate
        };

    } catch (e) {
        console.error("验证秘钥时发生错误: " + e);
        return {
            success: false,
            message: "验证过程中发生错误: " + e.message,
            expireDate: null
        };
    }
}

/**
 * 解密秘钥获取到期日期
 * 使用暴力破解方法：遍历所有可能的日期，与密钥组合进行MD5哈希比较
 * @param {string} licenseKey - 格式化的秘钥 (XXXX-XXXX-XXXX-XXXX)
 * @returns {string|null} 到期日期字符串或null
 */
function decryptLicense(licenseKey) {
    try {
        console.log("开始解密秘钥: " + licenseKey);

        // 1. 验证秘钥格式 (XXXX-XXXX-XXXX-XXXX)
        var keyPattern = /^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/;
        if (!keyPattern.test(licenseKey)) {
            console.log("秘钥格式不正确");
            return null;
        }

        // 2. 移除连字符，获取16位哈希，转为小写
        var targetHash = licenseKey.replace(/-/g, '').toLowerCase();
        console.log("目标哈希: " + targetHash);

        // 3. 密钥字符串（与PHP代码中的密钥保持一致）
        var secretKey = "黑猫AI-secret-key-123";

        // 4. 暴力破解：遍历所有可能的日期 (2020-2030)
        console.log("开始暴力破解，遍历日期范围 2020-2030...");

        for (var year = 2020; year <= 2030; year++) {
            for (var month = 1; month <= 12; month++) {
                for (var day = 1; day <= 31; day++) {
                    // 跳过无效日期
                    if (!isValidDate(year, month, day)) {
                        continue;
                    }

                    // 构造测试日期字符串 (格式: YYYYMMDD)
                    var testDate = year.toString() +
                                  (month < 10 ? "0" + month : month.toString()) +
                                  (day < 10 ? "0" + day : day.toString());

                    // 组合数据：日期 + 密钥
                    var mixedData = testDate + secretKey;

                    // 计算MD5哈希
                    var hash = md5(mixedData);

                    // 比较哈希的前16位
                    if (hash.substring(0, 16) === targetHash) {
                        // 找到匹配的日期！
                        var expireDate = year + "-" +
                                        (month < 10 ? "0" + month : month.toString()) + "-" +
                                        (day < 10 ? "0" + day : day.toString());

                        console.log("🎉 找到匹配日期: " + expireDate);
                        console.log("测试数据: " + mixedData);
                        console.log("生成哈希: " + hash);
                        console.log("匹配部分: " + hash.substring(0, 16));

                        return expireDate;
                    }
                }

                // 每处理完一个月，输出进度
                if (month % 3 === 0) {
                    console.log("处理进度: " + year + "年" + month + "月");
                }
            }
        }

        console.log("❌ 未找到匹配的日期");
        return null;

    } catch (e) {
        console.error("解密秘钥失败: " + e);
        return null;
    }
}

/**
 * 保存秘钥到配置文件
 * @param {string} licenseKey - 秘钥
 */
function saveLicenseToConfig(licenseKey) {
    try {
        var config = {
            licenseKey: licenseKey,
            savedAt: new Date().toISOString()
        };

        var configJson = JSON.stringify(config);
        files.write(LICENSE_CONFIG_FILE, configJson);
        console.log("秘钥已保存到配置文件");

    } catch (e) {
        console.error("保存秘钥到配置文件失败: " + e);
    }
}

/**
 * 从配置文件读取秘钥
 * @returns {string|null} 保存的秘钥或null
 */
function loadLicenseFromConfig() {
    try {
        if (files.exists(LICENSE_CONFIG_FILE)) {
            var configStr = files.read(LICENSE_CONFIG_FILE);
            var config = JSON.parse(configStr);
            console.log("从配置文件加载秘钥成功");
            return config.licenseKey || null;
        }
        return null;
    } catch (e) {
        console.error("从配置文件读取秘钥失败: " + e);
        return null;
    }
}

/**
 * 验证日期是否有效（年月日组合）
 * @param {number} year - 年份
 * @param {number} month - 月份 (1-12)
 * @param {number} day - 日期 (1-31)
 * @returns {boolean} 是否有效
 */
function isValidDate(year, month, day) {
    // 基本范围检查
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // 每月天数检查
    var daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 闰年检查
    if (month === 2 && isLeapYear(year)) {
        daysInMonth[1] = 29;
    }

    return day <= daysInMonth[month - 1];
}

/**
 * 检查是否为闰年
 * @param {number} year - 年份
 * @returns {boolean} 是否为闰年
 */
function isLeapYear(year) {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * MD5哈希函数
 * @param {string} str - 要哈希的字符串
 * @returns {string} MD5哈希值
 */
function md5(str) {
    // 简化的MD5实现，用于AutoJS环境
    // 注意：这是一个简化版本，实际项目中建议使用更完整的MD5库

    function rotateLeft(value, amount) {
        var lbits = value << amount;
        var rbits = value >>> (32 - amount);
        return lbits | rbits;
    }

    function addUnsigned(x, y) {
        var x4 = (x & 0x40000000);
        var y4 = (y & 0x40000000);
        var x8 = (x & 0x80000000);
        var y8 = (y & 0x80000000);
        var result = (x & 0x3FFFFFFF) + (y & 0x3FFFFFFF);
        if (x4 & y4) {
            return (result ^ 0x80000000 ^ x8 ^ y8);
        }
        if (x4 | y4) {
            if (result & 0x40000000) {
                return (result ^ 0xC0000000 ^ x8 ^ y8);
            } else {
                return (result ^ 0x40000000 ^ x8 ^ y8);
            }
        } else {
            return (result ^ x8 ^ y8);
        }
    }

    function F(x, y, z) { return (x & y) | ((~x) & z); }
    function G(x, y, z) { return (x & z) | (y & (~z)); }
    function H(x, y, z) { return (x ^ y ^ z); }
    function I(x, y, z) { return (y ^ (x | (~z))); }

    function FF(a, b, c, d, x, s, ac) {
        a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac));
        return addUnsigned(rotateLeft(a, s), b);
    }

    function GG(a, b, c, d, x, s, ac) {
        a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac));
        return addUnsigned(rotateLeft(a, s), b);
    }

    function HH(a, b, c, d, x, s, ac) {
        a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac));
        return addUnsigned(rotateLeft(a, s), b);
    }

    function II(a, b, c, d, x, s, ac) {
        a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac));
        return addUnsigned(rotateLeft(a, s), b);
    }

    function convertToWordArray(str) {
        var wordArray = [];
        var wordCount = (((str.length + 8) - ((str.length + 8) % 64)) / 64 + 1) * 16;
        for (var i = 0; i < wordCount; i++) {
            wordArray[i] = 0;
        }
        for (var i = 0; i < str.length; i++) {
            var bytePosition = (i - (i % 4)) / 4;
            var byteOffset = (i % 4) * 8;
            wordArray[bytePosition] = (wordArray[bytePosition] | (str.charCodeAt(i) << byteOffset));
        }
        var bytePosition = (str.length - (str.length % 4)) / 4;
        var byteOffset = (str.length % 4) * 8;
        wordArray[bytePosition] = wordArray[bytePosition] | (0x80 << byteOffset);
        wordArray[wordCount - 2] = str.length << 3;
        wordArray[wordCount - 1] = str.length >>> 29;
        return wordArray;
    }

    function wordToHex(value) {
        var hex = "";
        for (var i = 0; i <= 3; i++) {
            var byte = (value >>> (i * 8)) & 255;
            hex += ("0" + byte.toString(16)).substring(("0" + byte.toString(16)).length - 2);
        }
        return hex;
    }

    var x = convertToWordArray(str);
    var a = 0x67452301;
    var b = 0xEFCDAB89;
    var c = 0x98BADCFE;
    var d = 0x10325476;

    for (var i = 0; i < x.length; i += 16) {
        var olda = a;
        var oldb = b;
        var oldc = c;
        var oldd = d;

        a = FF(a, b, c, d, x[i + 0], 7, 0xD76AA478);
        d = FF(d, a, b, c, x[i + 1], 12, 0xE8C7B756);
        c = FF(c, d, a, b, x[i + 2], 17, 0x242070DB);
        b = FF(b, c, d, a, x[i + 3], 22, 0xC1BDCEEE);
        a = FF(a, b, c, d, x[i + 4], 7, 0xF57C0FAF);
        d = FF(d, a, b, c, x[i + 5], 12, 0x4787C62A);
        c = FF(c, d, a, b, x[i + 6], 17, 0xA8304613);
        b = FF(b, c, d, a, x[i + 7], 22, 0xFD469501);
        a = FF(a, b, c, d, x[i + 8], 7, 0x698098D8);
        d = FF(d, a, b, c, x[i + 9], 12, 0x8B44F7AF);
        c = FF(c, d, a, b, x[i + 10], 17, 0xFFFF5BB1);
        b = FF(b, c, d, a, x[i + 11], 22, 0x895CD7BE);
        a = FF(a, b, c, d, x[i + 12], 7, 0x6B901122);
        d = FF(d, a, b, c, x[i + 13], 12, 0xFD987193);
        c = FF(c, d, a, b, x[i + 14], 17, 0xA679438E);
        b = FF(b, c, d, a, x[i + 15], 22, 0x49B40821);

        a = GG(a, b, c, d, x[i + 1], 5, 0xF61E2562);
        d = GG(d, a, b, c, x[i + 6], 9, 0xC040B340);
        c = GG(c, d, a, b, x[i + 11], 14, 0x265E5A51);
        b = GG(b, c, d, a, x[i + 0], 20, 0xE9B6C7AA);
        a = GG(a, b, c, d, x[i + 5], 5, 0xD62F105D);
        d = GG(d, a, b, c, x[i + 10], 9, 0x2441453);
        c = GG(c, d, a, b, x[i + 15], 14, 0xD8A1E681);
        b = GG(b, c, d, a, x[i + 4], 20, 0xE7D3FBC8);
        a = GG(a, b, c, d, x[i + 9], 5, 0x21E1CDE6);
        d = GG(d, a, b, c, x[i + 14], 9, 0xC33707D6);
        c = GG(c, d, a, b, x[i + 3], 14, 0xF4D50D87);
        b = GG(b, c, d, a, x[i + 8], 20, 0x455A14ED);
        a = GG(a, b, c, d, x[i + 13], 5, 0xA9E3E905);
        d = GG(d, a, b, c, x[i + 2], 9, 0xFCEFA3F8);
        c = GG(c, d, a, b, x[i + 7], 14, 0x676F02D9);
        b = GG(b, c, d, a, x[i + 12], 20, 0x8D2A4C8A);

        a = HH(a, b, c, d, x[i + 5], 4, 0xFFFA3942);
        d = HH(d, a, b, c, x[i + 8], 11, 0x8771F681);
        c = HH(c, d, a, b, x[i + 11], 16, 0x6D9D6122);
        b = HH(b, c, d, a, x[i + 14], 23, 0xFDE5380C);
        a = HH(a, b, c, d, x[i + 1], 4, 0xA4BEEA44);
        d = HH(d, a, b, c, x[i + 4], 11, 0x4BDECFA9);
        c = HH(c, d, a, b, x[i + 7], 16, 0xF6BB4B60);
        b = HH(b, c, d, a, x[i + 10], 23, 0xBEBFBC70);
        a = HH(a, b, c, d, x[i + 13], 4, 0x289B7EC6);
        d = HH(d, a, b, c, x[i + 0], 11, 0xEAA127FA);
        c = HH(c, d, a, b, x[i + 3], 16, 0xD4EF3085);
        b = HH(b, c, d, a, x[i + 6], 23, 0x4881D05);
        a = HH(a, b, c, d, x[i + 9], 4, 0xD9D4D039);
        d = HH(d, a, b, c, x[i + 12], 11, 0xE6DB99E5);
        c = HH(c, d, a, b, x[i + 15], 16, 0x1FA27CF8);
        b = HH(b, c, d, a, x[i + 2], 23, 0xC4AC5665);

        a = II(a, b, c, d, x[i + 0], 6, 0xF4292244);
        d = II(d, a, b, c, x[i + 7], 10, 0x432AFF97);
        c = II(c, d, a, b, x[i + 14], 15, 0xAB9423A7);
        b = II(b, c, d, a, x[i + 5], 21, 0xFC93A039);
        a = II(a, b, c, d, x[i + 12], 6, 0x655B59C3);
        d = II(d, a, b, c, x[i + 3], 10, 0x8F0CCC92);
        c = II(c, d, a, b, x[i + 10], 15, 0xFFEFF47D);
        b = II(b, c, d, a, x[i + 1], 21, 0x85845DD1);
        a = II(a, b, c, d, x[i + 8], 6, 0x6FA87E4F);
        d = II(d, a, b, c, x[i + 15], 10, 0xFE2CE6E0);
        c = II(c, d, a, b, x[i + 6], 15, 0xA3014314);
        b = II(b, c, d, a, x[i + 13], 21, 0x4E0811A1);
        a = II(a, b, c, d, x[i + 4], 6, 0xF7537E82);
        d = II(d, a, b, c, x[i + 11], 10, 0xBD3AF235);
        c = II(c, d, a, b, x[i + 2], 15, 0x2AD7D2BB);
        b = II(b, c, d, a, x[i + 9], 21, 0xEB86D391);

        a = addUnsigned(a, olda);
        b = addUnsigned(b, oldb);
        c = addUnsigned(c, oldc);
        d = addUnsigned(d, oldd);
    }

    return (wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d)).toLowerCase();
}

// 导出函数供其他模块使用
module.exports = {
    validateLicense: validateLicense,
    loadLicenseFromConfig: loadLicenseFromConfig,
    saveLicenseToConfig: saveLicenseToConfig
};
