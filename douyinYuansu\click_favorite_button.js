/**
 * 点击抖音收藏按钮模块
 * 根据控件属性定位并模拟真人点击收藏按钮
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找收藏按钮的主函数
 * @returns {UiObject|null} 找到的收藏按钮控件，未找到返回null
 */
function findFavoriteButton() {
    console.log("开始查找收藏按钮..."); // 输出开始查找日志

    try {
        // 方法1: 优先通过desc关键词查找（不包含数值）
        console.log("方法1: 优先通过desc关键词查找..."); // 输出当前尝试的方法
        var buttonByDescContains = descContains("收藏").visibleToUser().findOne(2000); // 查找desc包含"收藏"的控件
        if (buttonByDescContains) {
            console.log("✓ 通过desc关键词找到收藏按钮，desc: " + buttonByDescContains.desc()); // 输出找到按钮的日志
            return buttonByDescContains; // 返回找到的按钮
        }

        // 方法2: 通过desc中包含"未选中"查找
        console.log("方法2: 尝试通过desc包含'未选中'查找..."); // 输出当前尝试的方法
        var buttonByUnchecked = descContains("未选中").visibleToUser().findOne(2000); // 查找desc包含"未选中"的控件
        if (buttonByUnchecked) {
            var uncheckedDesc = buttonByUnchecked.desc(); // 获取描述
            if (uncheckedDesc && uncheckedDesc.includes("收藏")) {
                console.log("✓ 通过'未选中'关键词找到收藏按钮，desc: " + uncheckedDesc); // 输出找到按钮的日志
                return buttonByUnchecked; // 返回找到的按钮
            }
        }

        // 方法3: 通过完整的资源ID查找
        console.log("方法3: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var buttonByFullId = id("com.ss.android.ugc.aweme:id/dt6").visibleToUser().findOne(2000); // 使用完整的资源ID，确保可见，等待2秒
        if (buttonByFullId) {
            console.log("✓ 通过完整资源ID找到收藏按钮"); // 输出找到按钮的日志
            return buttonByFullId; // 返回找到的按钮
        }

        // 方法4: 通过简短id查找
        console.log("方法4: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var buttonByShortId = id("dt6").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (buttonByShortId) {
            console.log("✓ 通过简短ID找到收藏按钮"); // 输出找到按钮的日志
            return buttonByShortId; // 返回找到的按钮
        }

        // 方法5: 通过className和属性组合查找
        console.log("方法5: 尝试通过className和属性组合查找..."); // 输出当前尝试的方法
        var buttonByClass = className("android.widget.LinearLayout") // 类名匹配
            .clickable(true) // 可点击
            .enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .depth(3) // 深度为3
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒

        if (buttonByClass) {
            // 进一步验证desc内容或ID
            var classDesc = buttonByClass.desc(); // 获取控件描述
            var classId = buttonByClass.id(); // 获取控件ID
            if ((classDesc && classDesc.includes("收藏")) ||
                (classId && classId.includes("dt6"))) {
                console.log("✓ 通过className组合找到收藏按钮，desc: " + classDesc + ", id: " + classId); // 输出找到按钮的日志
                return buttonByClass; // 返回找到的按钮
            }
        }

        // 方法6: 通过多重属性精确匹配查找
        console.log("方法6: 尝试通过多重属性精确匹配查找..."); // 输出当前尝试的方法
        var buttonByMultiProps = clickable(true) // 可点击
            .enabled(true) // 启用状态
            .focusable(true) // 可获得焦点
            .visibleToUser() // 对用户可见
            .packageName("com.ss.android.ugc.aweme") // 包名匹配
            .findOne(2000); // 等待2秒

        if (buttonByMultiProps) {
            // 进一步验证是否为收藏按钮
            var multiPropsDesc = buttonByMultiProps.desc(); // 获取控件描述
            var multiPropsId = buttonByMultiProps.id(); // 获取控件ID
            if ((multiPropsDesc && multiPropsDesc.includes("收藏")) ||
                (multiPropsId && multiPropsId.includes("dt6"))) {
                console.log("✓ 通过多重属性精确匹配找到收藏按钮，desc: " + multiPropsDesc); // 输出找到按钮的日志
                return buttonByMultiProps; // 返回找到的按钮
            }
        }

        // 方法7: 遍历所有可见控件查找
        console.log("方法7: 遍历所有可见控件查找..."); // 输出当前尝试的方法
        var allVisibleViews = clickable(true).enabled(true).focusable(true).visibleToUser().find(); // 查找所有可见的可点击控件
        console.log("找到 " + allVisibleViews.length + " 个符合条件的可见控件"); // 输出找到的控件数量

        for (var i = 0; i < allVisibleViews.length; i++) {
            var element = allVisibleViews[i]; // 遍历每个控件
            var elementId = element.id(); // 获取控件ID
            var elementDesc = element.desc(); // 获取控件描述
            var elementPackage = element.packageName(); // 获取包名
            var elementClass = element.className(); // 获取类名

            // 检查是否符合收藏按钮的特征
            if (elementPackage && elementPackage.includes("aweme") &&
                elementClass && elementClass.includes("LinearLayout") &&
                ((elementId && elementId.includes("dt6")) ||
                 (elementDesc && elementDesc.includes("收藏")))) {
                console.log("✓ 通过遍历找到收藏按钮 - ID: " + elementId + ", desc: " + elementDesc); // 输出找到按钮的日志
                return element; // 返回找到的按钮
            }
        }

        console.log("✗ 所有方法都未找到收藏按钮"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 查找收藏按钮时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} button - 要点击的按钮控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(button) {
    console.log("开始模拟真人点击收藏按钮操作..."); // 输出开始点击日志

    try {
        // 获取按钮的位置信息
        var bounds = button.bounds(); // 获取按钮边界
        if (!bounds) {
            console.error("✗ 无法获取按钮位置信息"); // 输出错误信息
            return false; // 返回失败
        }

        console.log("按钮位置: " + bounds.toString()); // 输出按钮位置

        // 计算按钮中心点坐标
        var centerX = bounds.centerX(); // 按钮中心X坐标
        var centerY = bounds.centerY(); // 按钮中心Y坐标

        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-8, 8); // X轴随机偏移-8到8像素
        var randomOffsetY = random(-6, 6); // Y轴随机偏移-6到6像素

        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标

        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标

        // 模拟真人的点击前停顿
        var preClickDelay = random(150, 400); // 点击前随机延迟150-400毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟

        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = press(clickX, clickY, random(80, 200)); // 执行坐标点击，随机按压时长

        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = button.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }

        // 模拟真人的点击后停顿
        var postClickDelay = random(300, 600); // 点击后随机延迟300-600毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟

        console.log("✓ 模拟真人点击收藏按钮操作完成"); // 输出完成日志
        return true; // 返回成功

    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击收藏按钮（只有未选中状态才点击）
 * @returns {boolean} 操作是否成功
 */
function clickFavoriteButton() {
    console.log("=== 开始执行智能收藏按钮操作 ==="); // 输出开始操作日志

    // 检查是否在抖音应用中
    if (!currentPackage().includes("aweme")) {
        console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
        launchApp("抖音"); // 启动抖音应用
        sleep(3000); // 等待3秒让应用加载
        waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    }

    // 查找收藏按钮
    var favoriteButton = findFavoriteButton(); // 调用查找函数

    if (!favoriteButton) {
        console.error("✗ 未找到收藏按钮，操作失败"); // 输出失败信息
        toast("未找到收藏按钮"); // 显示失败提示
        return false; // 返回失败
    }

    console.log("✓ 成功找到收藏按钮"); // 输出成功找到日志

    // 检查收藏状态
    var currentStatus = checkFavoriteStatus(); // 检查收藏状态
    console.log("📊 当前收藏状态: " + currentStatus); // 输出当前状态

    if (currentStatus === "已收藏") {
        console.log("ℹ️ 视频已收藏，跳过点击操作"); // 输出已收藏信息
        toast("视频已收藏，无需重复操作"); // 显示已收藏提示
        return true; // 返回成功（因为目标已达成）
    }

    if (currentStatus === "未收藏") {
        console.log("💖 视频未收藏，执行收藏操作..."); // 输出执行收藏信息
        toast("执行收藏操作..."); // 显示执行收藏提示

        // 执行模拟真人点击
        var clickResult = humanLikeClick(favoriteButton); // 调用点击函数

        if (clickResult) {
            console.log("✓ 收藏按钮点击成功"); // 输出成功日志
            toast("✓ 视频收藏成功"); // 显示成功提示

            // 等待状态更新并验证
            sleep(1500); // 等待1.5秒
            var newStatus = checkFavoriteStatus(); // 重新检查状态
            console.log("📊 操作后状态: " + newStatus); // 输出新状态

            if (newStatus === "已收藏") {
                console.log("🎉 收藏状态确认成功！"); // 输出确认成功信息
                return true; // 返回成功
            } else {
                console.log("⚠️ 收藏状态未如预期改变"); // 输出状态未改变警告
                return false; // 返回失败
            }
        } else {
            console.error("✗ 收藏按钮点击失败"); // 输出失败日志
            toast("✗ 收藏操作失败"); // 显示失败提示
            return false; // 返回失败
        }
    }

    // 状态未知的情况
    console.log("⚠️ 收藏状态未知，不执行点击操作"); // 输出状态未知信息
    toast("收藏状态未知，操作取消"); // 显示状态未知提示
    return false; // 返回失败
}

/**
 * 智能收藏函数：只在未选中状态时点击收藏
 * @returns {boolean} 操作是否成功
 */
function smartFavoriteClick() {
    console.log("=== 开始执行智能收藏操作（仅未选中时点击） ==="); // 输出开始操作日志

    // 查找收藏按钮
    var favoriteButton = findFavoriteButton(); // 调用查找函数

    if (!favoriteButton) {
        console.error("✗ 未找到收藏按钮"); // 输出失败信息
        toast("未找到收藏按钮"); // 显示失败提示
        return false; // 返回失败
    }

    // 获取按钮描述
    var buttonDesc = favoriteButton.desc(); // 获取按钮描述
    console.log("📝 收藏按钮描述: " + buttonDesc); // 输出按钮描述

    // 检查是否为"未选中"状态
    if (buttonDesc && buttonDesc.includes("未选中")) {
        console.log("✅ 检测到未选中状态，执行收藏操作..."); // 输出检测到未选中状态
        toast("执行收藏操作..."); // 显示执行收藏提示

        // 执行点击操作
        var clickResult = humanLikeClick(favoriteButton); // 调用点击函数

        if (clickResult) {
            console.log("✓ 收藏操作成功"); // 输出成功日志
            toast("✓ 收藏成功"); // 显示成功提示
            return true; // 返回成功
        } else {
            console.error("✗ 收藏操作失败"); // 输出失败日志
            toast("✗ 收藏失败"); // 显示失败提示
            return false; // 返回失败
        }
    } else if (buttonDesc && (buttonDesc.includes("已选中") || buttonDesc.includes("选中"))) {
        console.log("ℹ️ 视频已收藏，跳过操作"); // 输出已收藏信息
        toast("视频已收藏，无需重复操作"); // 显示已收藏提示
        return true; // 返回成功（目标已达成）
    } else {
        console.log("⚠️ 无法确定收藏状态，desc: " + buttonDesc); // 输出无法确定状态
        toast("无法确定收藏状态"); // 显示无法确定状态提示
        return false; // 返回失败
    }
}

/**
 * 检查收藏状态
 * @returns {string} 收藏状态："已收藏"、"未收藏"或"未知"
 */
function checkFavoriteStatus() {
    console.log("检查收藏状态..."); // 输出检查状态日志

    try {
        var favoriteButton = findFavoriteButton(); // 查找收藏按钮

        if (favoriteButton) {
            var desc = favoriteButton.desc(); // 获取按钮描述
            console.log("收藏按钮描述: " + desc); // 输出按钮描述

            if (desc) {
                if (desc.includes("未选中")) {
                    console.log("✓ 当前状态: 未收藏"); // 输出未收藏状态
                    return "未收藏"; // 返回未收藏状态
                } else if (desc.includes("已选中") || desc.includes("选中")) {
                    console.log("✓ 当前状态: 已收藏"); // 输出已收藏状态
                    return "已收藏"; // 返回已收藏状态
                }
            }
        }

        console.log("⚠️ 无法确定收藏状态"); // 输出无法确定状态
        return "未知"; // 返回未知状态

    } catch (error) {
        console.error("✗ 检查收藏状态时发生错误: " + error); // 输出错误信息
        return "未知"; // 返回未知状态
    }
}

/**
 * 测试函数：测试查找和点击功能
 */
function testClickFavoriteButton() {
    console.log("=== 开始测试点击收藏按钮功能 ==="); // 输出测试开始信息

    // 测试查找功能
    console.log("\n📝 测试1: 查找收藏按钮"); // 输出测试类型
    var button = findFavoriteButton(); // 查找按钮
    if (button) {
        console.log("✓ 查找测试成功"); // 输出测试成功
        console.log("按钮信息: ID=" + button.id() + ", desc=" + button.desc()); // 输出按钮信息
        console.log("按钮详细属性:"); // 输出详细属性标题
        console.log("  - className: " + button.className()); // 输出类名
        console.log("  - editable: " + button.editable); // 输出可编辑状态
        console.log("  - enabled: " + button.enabled()); // 输出启用状态
        console.log("  - focusable: " + button.focusable()); // 输出可获得焦点状态
        console.log("  - clickable: " + button.clickable()); // 输出可点击状态
        console.log("  - visibleToUser: " + button.visibleToUser()); // 输出可见性状态
        console.log("  - bounds: " + button.bounds()); // 输出边界信息
        console.log("  - depth: " + button.depth()); // 输出深度信息
        console.log("  - checked: " + button.checked); // 输出选中状态
    } else {
        console.log("✗ 查找测试失败"); // 输出测试失败
    }

    // 测试收藏状态检查
    console.log("\n📝 测试2: 检查收藏状态"); // 输出测试类型
    var status = checkFavoriteStatus(); // 检查收藏状态
    console.log("收藏状态检查结果: " + status); // 输出状态检查结果

    // 测试完整流程
    console.log("\n📝 测试3: 完整点击流程"); // 输出测试类型
    var result = clickFavoriteButton(); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果

    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

// 导出模块，供其他脚本调用
module.exports = {
    findFavoriteButton: findFavoriteButton, // 导出查找收藏按钮函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickFavoriteButton: clickFavoriteButton, // 导出主函数（智能收藏）
    smartFavoriteClick: smartFavoriteClick, // 导出智能收藏函数（仅未选中时点击）
    checkFavoriteStatus: checkFavoriteStatus, // 导出检查收藏状态函数
    testClickFavoriteButton: testClickFavoriteButton // 导出测试函数
};

// 如果直接运行此脚本，则执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("直接运行click_favorite_button.js，开始执行测试..."); // 输出直接运行提示
    testClickFavoriteButton(); // 执行测试函数
}
