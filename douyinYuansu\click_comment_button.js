/**
 * 点击抖音评论按钮模块
 * 根据控件属性定位并模拟真人点击评论按钮
 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

/**
 * 查找评论按钮的主函数
 * @returns {UiObject|null} 找到的评论按钮控件，未找到返回null
 */
function findCommentButton() {
    console.log("开始查找评论按钮..."); // 输出开始查找日志

    try {
        // 方法1: 通过完整的资源ID查找（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var buttonByFullId = id("com.ss.android.ugc.aweme:id/d5d").visibleToUser().findOne(3000); // 使用完整的资源ID，确保可见，等待3秒
        if (buttonByFullId) {
            console.log("✓ 通过完整资源ID找到评论按钮"); // 输出找到按钮的日志
            return buttonByFullId; // 返回找到的按钮
        }

        // 方法2: 通过简短id查找
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var buttonByShortId = id("d5d").visibleToUser().findOne(2000); // 使用简短id查找，确保可见，等待2秒
        if (buttonByShortId) {
            console.log("✓ 通过简短ID找到评论按钮"); // 输出找到按钮的日志
            return buttonByShortId; // 返回找到的按钮
        }

        // 方法3: 通过className和其他属性组合查找
        console.log("方法3: 尝试通过className和属性组合查找..."); // 输出当前尝试的方法
        var buttonByClass = className("android.widget.ImageView")
            .depth(3) // 根据您提供的控件信息，深度为3
            .enabled(true) // 控件必须是启用状态
            .clickable(true) // 控件必须是可点击的
            .visibleToUser() // 控件必须对用户可见
            .findOne(2000); // 等待2秒

        if (buttonByClass) {
            // 检查是否是评论按钮（通过desc属性）
            var buttonDesc = buttonByClass.desc(); // 获取控件描述
            if (buttonDesc && buttonDesc.includes("评论")) {
                console.log("✓ 通过className组合找到评论按钮，desc: " + buttonDesc); // 输出找到按钮的日志
                return buttonByClass; // 返回找到的按钮
            }
        }

        // 方法3.5: 通过属性组合和desc模式匹配查找
        console.log("方法3.5: 尝试通过属性组合和desc模式匹配查找..."); // 输出当前尝试的方法
        var buttonByPattern = className("android.widget.ImageView")
            .depth(3) // 深度为3
            .enabled(true) // 启用状态
            .clickable(true) // 可点击
            .visibleToUser() // 对用户可见
            .findOne(2000); // 等待2秒

        if (buttonByPattern) {
            var patternDesc = buttonByPattern.desc(); // 获取控件描述
            // 使用正则表达式匹配"评论"后跟数字的模式
            if (patternDesc && /评论\d*，按钮/.test(patternDesc)) {
                console.log("✓ 通过属性组合和desc模式匹配找到评论按钮，desc: " + patternDesc); // 输出找到按钮的日志
                return buttonByPattern; // 返回找到的按钮
            }
        }

        // 方法4: 通过desc关键词查找（不依赖具体数字）
        console.log("方法4: 尝试通过desc关键词查找..."); // 输出当前尝试的方法
        var buttonByDescKeyword = descContains("评论").descContains("按钮").visibleToUser().findOne(2000); // 查找同时包含"评论"和"按钮"的可见控件
        if (buttonByDescKeyword) {
            console.log("✓ 通过desc关键词找到评论按钮，desc: " + buttonByDescKeyword.desc()); // 输出找到按钮的日志
            return buttonByDescKeyword; // 返回找到的按钮
        }

        // 方法5: 通过单一desc关键词查找
        console.log("方法5: 尝试通过单一desc关键词查找..."); // 输出当前尝试的方法
        var buttonByDescContains = descContains("评论").visibleToUser().findOne(2000); // 查找desc包含"评论"的可见控件
        if (buttonByDescContains) {
            // 进一步验证是否为按钮类型的控件
            var singleDesc = buttonByDescContains.desc(); // 获取控件描述
            if (singleDesc && (singleDesc.includes("按钮") || buttonByDescContains.clickable())) {
                console.log("✓ 通过单一desc关键词找到评论按钮，desc: " + singleDesc); // 输出找到按钮的日志
                return buttonByDescContains; // 返回找到的按钮
            }
        }
        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 查找评论按钮时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示查找失败
    }
}

/**
 * 模拟真人点击操作
 * @param {UiObject} button - 要点击的按钮控件
 * @returns {boolean} 点击是否成功
 */
function humanLikeClick(button) {
    console.log("开始模拟真人点击操作..."); // 输出开始点击日志

    try {
        // 获取按钮的位置信息
        var bounds = button.bounds(); // 获取按钮边界
        if (!bounds) {
            console.error("✗ 无法获取按钮位置信息"); // 输出错误信息
            return false; // 返回失败
        }

        console.log("按钮位置: " + bounds.toString()); // 输出按钮位置

        // 计算按钮中心点坐标
        var centerX = bounds.centerX(); // 按钮中心X坐标
        var centerY = bounds.centerY(); // 按钮中心Y坐标

        // 添加随机偏移，模拟真人点击的不精确性
        var randomOffsetX = random(-5, 5); // X轴随机偏移-5到5像素
        var randomOffsetY = random(-5, 5); // Y轴随机偏移-5到5像素

        var clickX = centerX + randomOffsetX; // 最终点击X坐标
        var clickY = centerY + randomOffsetY; // 最终点击Y坐标

        console.log("计算的点击坐标: (" + clickX + ", " + clickY + ")"); // 输出点击坐标

        // 模拟真人的点击前停顿
        var preClickDelay = random(100, 300); // 点击前随机延迟100-300毫秒
        console.log("点击前延迟: " + preClickDelay + "ms"); // 输出延迟时间
        sleep(preClickDelay); // 执行延迟

        // 执行点击操作
        console.log("执行点击操作..."); // 输出点击日志
        var clickSuccess = click(clickX, clickY); // 执行坐标点击

        if (clickSuccess) {
            console.log("✓ 坐标点击成功"); // 输出成功日志
        } else {
            console.log("⚠️ 坐标点击可能失败，尝试控件点击..."); // 输出警告日志
            // 备用方案：直接点击控件
            clickSuccess = button.click(); // 直接点击控件
            if (clickSuccess) {
                console.log("✓ 控件点击成功"); // 输出成功日志
            } else {
                console.error("✗ 控件点击也失败"); // 输出失败日志
                return false; // 返回失败
            }
        }

        // 模拟真人的点击后停顿
        var postClickDelay = random(200, 500); // 点击后随机延迟200-500毫秒
        console.log("点击后延迟: " + postClickDelay + "ms"); // 输出延迟时间
        sleep(postClickDelay); // 执行延迟

        console.log("✓ 模拟真人点击操作完成"); // 输出完成日志
        return true; // 返回成功

    } catch (error) {
        console.error("✗ 模拟点击时发生错误: " + error); // 输出错误信息
        return false; // 返回失败
    }
}

/**
 * 主函数：查找并点击评论按钮
 * @returns {boolean} 操作是否成功
 */
function clickCommentButton() {
    console.log("=== 开始执行点击评论按钮操作 ==="); // 输出开始操作日志

    // 检查是否在抖音应用中
    // if (!currentPackage().includes("aweme")) {
    //     console.log("当前不在抖音应用中，尝试启动抖音..."); // 输出提示信息
    //     launchApp("抖音"); // 启动抖音应用
    //     sleep(3000); // 等待3秒让应用加载
    //     waitForPackage("com.ss.android.ugc.aweme"); // 等待抖音包名出现
    // }

    // 查找评论按钮
    var commentButton = findCommentButton(); // 调用查找函数

    if (!commentButton) {
        console.error("✗ 未找到评论按钮，操作失败"); // 输出失败信息
        toast("未找到评论按钮"); // 显示失败提示
        return false; // 返回失败
    }

    console.log("✓ 成功找到评论按钮"); // 输出成功找到日志
    toast("找到评论按钮，准备点击"); // 显示找到提示

    // 执行模拟真人点击
    var clickResult = humanLikeClick(commentButton); // 调用点击函数

    if (clickResult) {
        console.log("✓ 评论按钮点击成功"); // 输出成功日志
        toast("✓ 评论按钮点击成功"); // 显示成功提示
        return true; // 返回成功
    } else {
        console.error("✗ 评论按钮点击失败"); // 输出失败日志
        toast("✗ 评论按钮点击失败"); // 显示失败提示
        return false; // 返回失败
    }
}

/**
 * 测试函数：测试查找和点击功能
 */
function testClickCommentButton() {
    console.log("=== 开始测试点击评论按钮功能 ==="); // 输出测试开始信息

    // 测试查找功能
    console.log("\n📝 测试1: 查找评论按钮"); // 输出测试类型
    var button = findCommentButton(); // 查找按钮
    if (button) {
        console.log("✓ 查找测试成功"); // 输出测试成功
        console.log("按钮信息: ID=" + button.id() + ", desc=" + button.desc()); // 输出按钮信息
        console.log("按钮详细属性:"); // 输出详细属性标题
        console.log("  - className: " + button.className()); // 输出类名
        console.log("  - depth: " + button.depth()); // 输出深度
        console.log("  - enabled: " + button.enabled()); // 输出启用状态
        console.log("  - clickable: " + button.clickable()); // 输出可点击状态
        console.log("  - focusable: " + button.focusable()); // 输出可获得焦点状态
        console.log("  - bounds: " + button.bounds()); // 输出边界信息
    } else {
        console.log("✗ 查找测试失败"); // 输出测试失败
    }

    // 测试完整流程
    console.log("\n📝 测试2: 完整点击流程"); // 输出测试类型
    var result = clickCommentButton(); // 执行完整流程
    console.log("完整流程结果: " + (result ? "成功" : "失败")); // 输出结果

    console.log("\n=== 测试完成 ==="); // 输出测试结束信息
}

/**
 * 专门测试基于新属性的查找方法
 */
function testNewAttributeSearch() {
    console.log("=== 开始测试基于新属性的查找方法 ==="); // 输出测试开始信息

    // 测试方法1: 通过新的ID查找
    console.log("\n📝 测试新ID查找 (d5d)"); // 输出测试类型
    var buttonById = id("d5d").visibleToUser().findOne(3000); // 通过新ID查找可见控件
    if (buttonById) {
        console.log("✓ 通过新ID找到按钮"); // 输出成功信息
        console.log("按钮desc: " + buttonById.desc()); // 输出按钮描述
        console.log("按钮可见性: " + buttonById.visibleToUser()); // 输出可见性状态
    } else {
        console.log("✗ 通过新ID未找到按钮"); // 输出失败信息
    }

    // 测试方法2: 通过desc关键词组合查找
    console.log("\n📝 测试desc关键词组合查找 (评论+按钮)"); // 输出测试类型
    var buttonByDesc = descContains("评论").descContains("按钮").visibleToUser().findOne(3000); // 通过关键词组合查找可见控件
    if (buttonByDesc) {
        console.log("✓ 通过desc关键词组合找到按钮"); // 输出成功信息
        console.log("按钮ID: " + buttonByDesc.id()); // 输出按钮ID
        console.log("按钮Desc: " + buttonByDesc.desc()); // 输出按钮描述
        console.log("按钮可见性: " + buttonByDesc.visibleToUser()); // 输出可见性状态
    } else {
        console.log("✗ 通过desc关键词组合未找到按钮"); // 输出失败信息
    }

    // 测试方法3: 通过属性组合查找
    console.log("\n📝 测试属性组合查找"); // 输出测试类型
    var buttonByProps = className("android.widget.ImageView")
        .depth(3)
        .enabled(true)
        .clickable(true)
        .focusable(true)
        .visibleToUser() // 确保对用户可见
        .findOne(3000); // 通过属性组合查找

    if (buttonByProps) {
        var propsDesc = buttonByProps.desc(); // 获取描述
        if (propsDesc && propsDesc.includes("评论")) {
            console.log("✓ 通过属性组合找到评论按钮"); // 输出成功信息
            console.log("按钮完整信息: ID=" + buttonByProps.id() + ", desc=" + propsDesc); // 输出完整信息
        } else {
            console.log("⚠️ 找到ImageView但不是评论按钮，desc: " + propsDesc); // 输出警告信息
        }
    } else {
        console.log("✗ 通过属性组合未找到按钮"); // 输出失败信息
    }

    console.log("\n=== 新属性测试完成 ==="); // 输出测试结束信息
}

/**
 * 随机数生成函数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
function random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min; // 生成指定范围的随机整数
}

// 导出模块，供其他脚本调用
module.exports = {
    findCommentButton: findCommentButton, // 导出查找评论按钮函数
    humanLikeClick: humanLikeClick, // 导出模拟真人点击函数
    clickCommentButton: clickCommentButton, // 导出主函数
    testClickCommentButton: testClickCommentButton, // 导出测试函数
    testNewAttributeSearch: testNewAttributeSearch // 导出新属性测试函数
};

// 如果直接运行此脚本，则执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("直接运行click_comment_button.js，开始执行测试..."); // 输出直接运行提示

    // 首先测试基于新属性的查找方法
    testNewAttributeSearch(); // 执行新属性测试函数

    // 然后执行完整的测试流程
    testClickCommentButton(); // 执行完整测试函数
}
