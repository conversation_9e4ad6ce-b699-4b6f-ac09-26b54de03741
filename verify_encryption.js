// 验证加密逻辑的脚本

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 验证加密逻辑 ===");

// 测试目标数据
var targetDate = "20250606";
var secretKey = "黑猫AI-secret-key-123";
var mixedData = targetDate + secretKey;
var expectedHash = "7dce36a762eb24f5";

console.log("目标日期: " + targetDate);
console.log("密钥: " + secretKey);
console.log("组合数据: " + mixedData);
console.log("期望哈希前16位: " + expectedHash);

// 使用我们的MD5函数计算哈希
console.log("\n=== 计算MD5哈希 ===");
var calculatedHash = licenseValidator.md5(mixedData);
console.log("计算得到的完整哈希: " + calculatedHash);
console.log("前16位: " + calculatedHash.substring(0, 16));

// 验证是否匹配
var matches = calculatedHash.substring(0, 16) === expectedHash;
console.log("哈希匹配: " + (matches ? "✅ 是" : "❌ 否"));

if (!matches) {
    console.log("❌ MD5计算结果不匹配！");
    console.log("期望: " + expectedHash);
    console.log("实际: " + calculatedHash.substring(0, 16));
    console.log("这可能表明MD5实现有问题或者加密逻辑不正确");
}

// 测试一些已知的MD5值来验证我们的MD5实现
console.log("\n=== 验证MD5实现 ===");
var testCases = [
    {input: "hello", expected: "5d41402abc4b2a76b9719d911017c592"},
    {input: "test", expected: "098f6bcd4621d373cade4e832627b4f6"},
    {input: "", expected: "d41d8cd98f00b204e9800998ecf8427e"}
];

for (var i = 0; i < testCases.length; i++) {
    var testCase = testCases[i];
    var result = licenseValidator.md5(testCase.input);
    var correct = result === testCase.expected;
    console.log("测试 '" + testCase.input + "': " + (correct ? "✅" : "❌"));
    if (!correct) {
        console.log("  期望: " + testCase.expected);
        console.log("  实际: " + result);
    }
}

// 直接测试解密
console.log("\n=== 测试解密功能 ===");
var result = licenseValidator.validateLicense("7DCE-36A7-62EB-24F5");
console.log("解密结果:", JSON.stringify(result, null, 2));

if (result.success && result.expireDate === "2025-06-06") {
    console.log("✅ 解密正确！");
} else {
    console.log("❌ 解密错误");
    console.log("期望: 2025-06-06");
    console.log("实际: " + (result.expireDate || "null"));
}
