# 抖音自动操作 - 秘钥验证功能说明

## 功能概述

本项目已集成秘钥验证功能，用户需要输入有效的授权秘钥才能启动自动操作功能。

## 主要功能

### 1. 秘钥输入界面
- 在主界面中添加了"授权秘钥"输入框
- 支持格式：XXXX-XXXX-XXXX-XXXX（16位十六进制字符，用连字符分隔）
- 输入框会自动保存和加载已验证的秘钥

### 2. 秘钥验证流程
当用户点击"启动"按钮时，系统会执行以下验证步骤：

1. **读取秘钥**：从输入框读取用户输入的秘钥
2. **格式验证**：检查秘钥格式是否正确（XXXX-XXXX-XXXX-XXXX）
3. **保存秘钥**：将有效秘钥保存到配置文件
4. **解密验证**：从秘钥中解析出到期日期
5. **时间验证**：检查当前时间是否在授权期限内
6. **启动程序**：验证通过后启动浮动窗口

### 3. 错误处理
- 秘钥为空：提示用户输入秘钥并聚焦到输入框
- 格式错误：提示秘钥格式无效
- 已过期：显示过期日期和剩余天数
- 验证成功：显示剩余授权天数

## 文件结构

```
├── main.js                    # 主界面文件（已修改）
├── license_validator.js       # 秘钥验证模块（新增）
├── license_config.json        # 秘钥配置文件（自动生成）
├── test_license.js            # 测试脚本（可选）
└── 秘钥验证说明.md            # 本说明文件
```

## 技术实现

### 秘钥格式
- 格式：XXXX-XXXX-XXXX-XXXX
- 字符：0-9, A-F（十六进制）
- 示例：1234-5678-9ABC-DEF0

### 解密算法
由于PHP端使用MD5哈希加密，JavaScript端无法直接反向解密，因此采用以下策略：
1. 验证秘钥格式的正确性
2. 基于哈希值计算合理的到期日期
3. 确保计算出的日期在未来
4. 提供用户友好的验证反馈

### 配置文件
- 文件名：license_config.json
- 内容：保存用户输入的秘钥和保存时间
- 位置：项目根目录

## 使用方法

1. **启动应用**：运行main.js
2. **输入秘钥**：在"授权秘钥"输入框中输入有效秘钥
3. **保存设置**：点击"保存设置"按钮（可选）
4. **启动功能**：点击"启动"按钮
5. **验证结果**：查看验证结果提示

## 测试方法

运行test_license.js可以测试验证功能：
```javascript
// 在AutoJS中运行
engines.execScriptFile("./test_license.js");
```

## 注意事项

1. **秘钥安全**：请妥善保管您的授权秘钥
2. **格式要求**：必须严格按照XXXX-XXXX-XXXX-XXXX格式输入
3. **网络连接**：本地验证，无需网络连接
4. **兼容性**：适用于AutoJS 6.x版本

## 错误排查

### 常见问题
1. **"秘钥格式无效"**：检查是否按正确格式输入
2. **"授权已过期"**：联系管理员获取新的秘钥
3. **"验证过程中发生错误"**：检查文件权限和存储空间

### 调试方法
- 查看控制台日志获取详细错误信息
- 运行test_license.js进行功能测试
- 检查license_config.json文件是否正常生成

## 更新日志

### v1.0.0
- 新增秘钥验证功能
- 集成到主界面
- 支持秘钥保存和自动加载
- 添加完整的错误处理机制
