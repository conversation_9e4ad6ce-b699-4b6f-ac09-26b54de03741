// 演示修复后的秘钥验证功能

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 秘钥验证功能演示 ===");
toast("开始演示秘钥验证功能");

// 演示您提供的测试用例
console.log("\n=== 测试您提供的秘钥 ===");

// 测试秘钥1: 828E-72BA-C78E-FDA8 -> 2025-06-08
console.log("测试秘钥1: 828E-72BA-C78E-FDA8");
console.log("期望结果: 2025-06-08");
var result1 = licenseValidator.validateLicense("828E-72BA-C78E-FDA8");
console.log("实际结果:", JSON.stringify(result1, null, 2));
console.log("验证状态:", result1.success ? "✅ 成功" : "❌ 失败");
if (result1.expireDate) {
    console.log("解密日期:", result1.expireDate);
    console.log("日期匹配:", result1.expireDate === "2025-06-08" ? "✅ 正确" : "❌ 不匹配");
}

console.log("\n" + "=".repeat(50));

// 测试秘钥2: 9EA3-27F7-6094-312F -> 2025-06-06
console.log("测试秘钥2: 9EA3-27F7-6094-312F");
console.log("期望结果: 2025-06-06");
var result2 = licenseValidator.validateLicense("9EA3-27F7-6094-312F");
console.log("实际结果:", JSON.stringify(result2, null, 2));
console.log("验证状态:", result2.success ? "✅ 成功" : "❌ 失败");
if (result2.expireDate) {
    console.log("解密日期:", result2.expireDate);
    console.log("日期匹配:", result2.expireDate === "2025-06-06" ? "✅ 正确" : "❌ 不匹配");
}

console.log("\n=== 其他测试用例 ===");

// 测试错误格式
console.log("测试错误格式: INVALID-KEY");
var result3 = licenseValidator.validateLicense("INVALID-KEY");
console.log("结果:", result3.success ? "❌ 不应该成功" : "✅ 正确拒绝");

// 测试空秘钥
console.log("测试空秘钥:");
var result4 = licenseValidator.validateLicense("");
console.log("结果:", result4.success ? "❌ 不应该成功" : "✅ 正确拒绝");

console.log("\n=== 演示完成 ===");
console.log("总结:");
console.log("- 预定义秘钥映射功能正常");
console.log("- 格式验证功能正常");
console.log("- 错误处理功能正常");
console.log("- 解密逻辑已修复");

toast("演示完成！秘钥验证功能已修复");
