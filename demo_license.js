// 演示逆向工程解密算法

// 引入验证模块
var licenseValidator = require("./license_validator.js");

console.log("=== 逆向工程解密算法测试 ===");
toast("开始测试逆向解密算法");

// 测试已知样本
console.log("\n=== 测试已知样本 ===");

// 测试秘钥: 9EA3-27F7-6094-312F -> 应该解密为 2025-06-06
console.log("测试秘钥: 9EA3-27F7-6094-312F");
console.log("期望结果: 2025-06-06");

var result = licenseValidator.validateLicense("9EA3-27F7-6094-312F");
console.log("实际结果:", JSON.stringify(result, null, 2));

if (result.success) {
    console.log("✅ 验证成功");
    console.log("解密日期:", result.expireDate);

    if (result.expireDate === "2025-06-06") {
        console.log("🎉 解密结果完全正确！");
    } else {
        console.log("❌ 解密结果不匹配");
        console.log("期望: 2025-06-06");
        console.log("实际: " + result.expireDate);
    }
} else {
    console.log("❌ 验证失败:", result.message);
}

console.log("\n=== 分析解密过程 ===");

// 手动分析秘钥
var testKey = "9EA3-27F7-6094-312F";
var hashKey = testKey.replace(/-/g, '').toLowerCase();
console.log("原始秘钥:", testKey);
console.log("哈希值:", hashKey);

var part1 = hashKey.substring(0, 4);   // 9ea3
var part2 = hashKey.substring(4, 8);   // 27f7
var part3 = hashKey.substring(8, 12);  // 6094
var part4 = hashKey.substring(12, 16); // 312f

console.log("分段:");
console.log("  part1:", part1, "->", parseInt(part1, 16));
console.log("  part2:", part2, "->", parseInt(part2, 16));
console.log("  part3:", part3, "->", parseInt(part3, 16));
console.log("  part4:", part4, "->", parseInt(part4, 16));

var val1 = parseInt(part1, 16); // 40611
var val2 = parseInt(part2, 16); // 10231
var val3 = parseInt(part3, 16); // 24724
var val4 = parseInt(part4, 16); // 12591

console.log("目标日期: 2025-06-06");
console.log("需要找到的关系:");
console.log("  " + val1 + " -> 2025 (年份)");
console.log("  " + val2 + " -> 6 (月份)");
console.log("  " + val3 + " -> 6 (日期)");

// 尝试不同的数学关系
console.log("\n=== 尝试找到数学关系 ===");

// 年份分析
console.log("年份分析 (" + val1 + " -> 2025):");
console.log("  2020 + (" + val1 + " % 10) =", 2020 + (val1 % 10));
console.log("  2000 + (" + val1 + " % 50) =", 2000 + (val1 % 50));
console.log("  2024 + (" + val1 + " % 5) =", 2024 + (val1 % 5));

// 月份分析
console.log("月份分析 (" + val2 + " -> 6):");
console.log("  (" + val2 + " % 12) + 1 =", (val2 % 12) + 1);
console.log("  (" + val2 + " % 6) + 1 =", (val2 % 6) + 1);
console.log("  Math.floor((" + val2 + " / 100) % 12) + 1 =", Math.floor((val2 / 100) % 12) + 1);

// 日期分析
console.log("日期分析 (" + val3 + " -> 6):");
console.log("  (" + val3 + " % 31) + 1 =", (val3 % 31) + 1);
console.log("  (" + val3 + " % 28) + 1 =", (val3 % 28) + 1);
console.log("  Math.floor((" + val3 + " / 1000) % 31) + 1 =", Math.floor((val3 / 1000) % 31) + 1);

console.log("\n=== 测试完成 ===");
toast("逆向工程分析完成，请查看控制台");
