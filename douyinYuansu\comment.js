
/** 获取抖音用户简介文字 */

// 请求无障碍服务权限
if (!auto.service) {
    toast("请先开启无障碍服务"); // 提示用户开启无障碍服务
    auto.waitFor(); // 等待无障碍服务开启
}

// 获取用户简介文字的主函数
function getUserDescription() {
    console.log("开始获取抖音用户简介..."); // 输出日志，表示开始获取简介

    try {
        // 方法1: 通过完整的资源ID查找简介控件（最准确的方法）
        console.log("方法1: 尝试通过完整资源ID查找..."); // 输出当前尝试的方法
        var descByFullId = id("com.ss.android.ugc.aweme:id/desc").visibleToUser().findOne(3000); // 使用完整的资源ID，等待3秒
        if (descByFullId && descByFullId.text()) {
            var descText = descByFullId.text().trim(); // 获取文字内容并去除首尾空格
            if (descText !== "") {
                console.log("✓ 通过完整资源ID成功获取到简介: " + descText); // 输出获取到的简介
                return descText; // 返回简介文字
            }
        }

        // 方法2: 通过简短id查找简介控件
        console.log("方法2: 尝试通过简短ID查找..."); // 输出当前尝试的方法
        var descElement = id("desc").visibleToUser().findOne(2000); // 使用简短id查找，等待2秒
        if (descElement && descElement.text()) {
            var descText2 = descElement.text().trim(); // 获取文字内容并去除首尾空格
            if (descText2 !== "") {
                console.log("✓ 通过简短ID成功获取到简介: " + descText2); // 输出获取到的简介
                return descText2; // 返回简介文字
            }
        }

        // 方法3: 通过className和id组合查找
        console.log("方法3: 尝试通过className和ID组合查找..."); // 输出当前尝试的方法
        var descByClass = className("android.widget.TextView").id("desc").visibleToUser().findOne(2000); // 通过类名和id组合查找
        if (descByClass && descByClass.text()) {
            var descText3 = descByClass.text().trim(); // 获取文字内容并去除首尾空格
            if (descText3 !== "") {
                console.log("✓ 通过className组合成功获取到简介: " + descText3); // 输出获取到的简介
                return descText3; // 返回简介文字
            }
        }

        // 方法4: 通过深度和其他属性查找（基于您提供的控件信息）
        console.log("方法4: 尝试通过深度和属性查找..."); // 输出当前尝试的方法
        var descByDepth = className("android.widget.TextView")
            .depth(4) // 根据您提供的控件信息，深度为4
            .visibleToUser()
            .findOne(2000); // 等待2秒
        if (descByDepth && descByDepth.text()) {
            var elementId = descByDepth.id(); // 获取控件ID
            // 检查是否是简介相关的控件
            if (elementId && elementId.includes("desc")) {
                var descText4 = descByDepth.text().trim(); // 获取文字内容
                if (descText4 !== "") {
                    console.log("✓ 通过深度查找成功获取到简介: " + descText4); // 输出获取到的简介
                    return descText4; // 返回简介文字
                }
            }
        }

        // 方法5: 遍历所有TextView控件查找简介
        console.log("方法5: 遍历所有TextView控件查找..."); // 输出当前尝试的方法
        var allTextViews = className("android.widget.TextView").visibleToUser().find(); // 查找所有TextView控件
        console.log("找到 " + allTextViews.length + " 个TextView控件"); // 输出找到的控件数量

        for (var i = 0; i < allTextViews.length; i++) {
            var element = allTextViews[i]; // 遍历每个控件
            var elementId = element.id(); // 获取控件ID
            var elementText = element.text(); // 获取控件文字

            // 检查是否是简介相关的控件
            if (elementId && elementId.includes("desc") && elementText && elementText.trim() !== "") {
                console.log("✓ 通过遍历找到简介控件 - ID: " + elementId + ", 内容: " + elementText.trim()); // 输出找到的简介
                return elementText.trim(); // 返回简介文字
            }
        }

        // 方法6: 通过文字内容特征查找（备用方法）
        console.log("方法6: 尝试通过文字特征查找..."); // 输出当前尝试的方法
        var textElements = className("android.widget.TextView").find(); // 查找所有TextView
        for (var j = 0; j < textElements.length; j++) {
            var textElement = textElements[j]; // 遍历每个文字控件
            var text = textElement.text(); // 获取文字内容

            // 检查文字是否可能是用户简介（长度大于10且小于500字符）
            if (text && text.trim().length > 10 && text.trim().length < 500) {
                var bounds = textElement.bounds(); // 获取控件位置
                // 检查控件是否在屏幕可见区域内
                if (bounds && bounds.top > 0 && bounds.left > 0) {
                    console.log("✓ 通过文字特征可能找到简介: " + text.trim()); // 输出可能的简介
                    return text.trim(); // 返回可能的简介文字
                }
            }
        }

        console.log("✗ 所有方法都未找到用户简介"); // 如果所有方法都没找到，输出提示
        return null; // 返回null表示未找到

    } catch (error) {
        console.error("✗ 获取用户简介时发生错误: " + error); // 输出错误信息
        console.error("错误堆栈: " + error.stack); // 输出错误堆栈信息
        return null; // 返回null表示获取失败
    }
}
// var desc=getUserDescription()
// console.log("✓ 通过文字特征可能找到简介: " + desc); // 输出可能的简介


// 导出模块，供其他脚本调用
module.exports = {
    getUserDescription: getUserDescription, // 导出获取简介的主函数
};

