// 调试MD5实现的脚本

var licenseValidator = require("./license_validator.js");

console.log("=== 调试MD5实现 ===");

// 先测试一些已知的MD5值来验证我们的实现
var knownTests = [
    {input: "", expected: "d41d8cd98f00b204e9800998ecf8427e"},
    {input: "a", expected: "0cc175b9c0f1b6a831c399e269772661"},
    {input: "abc", expected: "900150983cd24fb0d6963f7d28e17f72"},
    {input: "hello", expected: "5d41402abc4b2a76b9719d911017c592"},
    {input: "hello world", expected: "5eb63bbbe01eeed093cb22bb8f5acdc3"}
];

console.log("测试已知MD5值:");
var md5Works = true;

for (var i = 0; i < knownTests.length; i++) {
    var test = knownTests[i];
    var result = licenseValidator.md5(test.input);
    var correct = result === test.expected;

    console.log("输入: '" + test.input + "'");
    console.log("期望: " + test.expected);
    console.log("实际: " + result);
    console.log("正确: " + (correct ? "✅" : "❌"));
    console.log("---");

    if (!correct) {
        md5Works = false;
    }
}

if (!md5Works) {
    console.log("❌ MD5实现有问题！");
    return;
} else {
    console.log("✅ MD5实现正确！");
}

// 测试目标组合
console.log("\n=== 测试目标组合 ===");
var targetDate = "20250606";
var secretKey = "黑猫AI-secret-key-123";
var mixedData = targetDate + secretKey;
var expectedHash = "7dce36a762eb24f5";

console.log("目标日期: " + targetDate);
console.log("密钥: " + secretKey);
console.log("组合数据: " + mixedData);
console.log("期望哈希前16位: " + expectedHash);

// 计算实际的MD5
var actualHash = licenseValidator.md5(mixedData);
console.log("实际完整哈希: " + actualHash);
console.log("实际前16位: " + actualHash.substring(0, 16));
console.log("匹配: " + (actualHash.substring(0, 16) === expectedHash ? "✅" : "❌"));

if (actualHash.substring(0, 16) !== expectedHash) {
    console.log("\n❌ 哈希不匹配！");
    console.log("这说明:");
    console.log("1. 期望的哈希值可能不正确");
    console.log("2. 或者组合的字符串不正确");
    console.log("3. 或者MD5实现有问题");
} else {
    console.log("\n✅ 哈希匹配正确！");
}

// 尝试一些可能的变体
console.log("\n=== 尝试可能的变体 ===");
var variants = [
    "20250606黑猫AI-secret-key-123",
    "2025-06-06黑猫AI-secret-key-123",
    "20250606黑猫AI-SECRET-KEY-123",
    "20250606heimaoai-secret-key-123"
];

console.log("可能的组合变体:");
for (var j = 0; j < variants.length; j++) {
    console.log((j+1) + ". " + variants[j]);
}

console.log("\n请分别用在线MD5工具测试这些变体");
console.log("看哪个的前16位是: " + expectedHash);

// 测试字符编码
console.log("\n=== 字符编码信息 ===");
console.log("密钥字符串: " + secretKey);
console.log("字符串长度: " + secretKey.length);

for (var k = 0; k < Math.min(secretKey.length, 10); k++) {
    var char = secretKey.charAt(k);
    var code = secretKey.charCodeAt(k);
    console.log("字符 '" + char + "' 编码: " + code);
}

console.log("\n=== 建议 ===");
console.log("1. 用在线MD5工具验证组合字符串");
console.log("2. 确认期望的哈希值是否正确");
console.log("3. 检查字符编码是否一致");
