// 快速测试MD5和解密逻辑

var licenseValidator = require("./license_validator.js");

console.log("=== 快速MD5测试 ===");

// 测试已知的MD5值
var testCases = [
    {input: "hello", expected: "5d41402abc4b2a76b9719d911017c592"},
    {input: "test", expected: "098f6bcd4621d373cade4e832627b4f6"}
];

for (var i = 0; i < testCases.length; i++) {
    var testCase = testCases[i];
    var result = licenseValidator.md5(testCase.input);
    var correct = result === testCase.expected;
    console.log("MD5('" + testCase.input + "'): " + (correct ? "✅" : "❌"));
    if (!correct) {
        console.log("  期望: " + testCase.expected);
        console.log("  实际: " + result);
    }
}

console.log("\n=== 测试目标组合 ===");

// 测试我们的目标组合
var targetDate = "20250606";
var secretKey = "黑猫AI-secret-key-123";
var mixedData = targetDate + secretKey;
var expectedHash = "7dce36a762eb24f5";

console.log("组合数据: " + mixedData);
var hash = licenseValidator.md5(mixedData);
console.log("完整哈希: " + hash);
console.log("前16位: " + hash.substring(0, 16));
console.log("期望前16位: " + expectedHash);
console.log("匹配: " + (hash.substring(0, 16) === expectedHash ? "✅" : "❌"));

console.log("\n=== 测试解密 ===");

// 测试解密功能
var result = licenseValidator.validateLicense("7DCE-36A7-62EB-24F5");
console.log("解密结果: " + JSON.stringify(result, null, 2));

if (result.success && result.expireDate === "2025-06-06") {
    console.log("🎉 解密成功！");
} else {
    console.log("❌ 解密失败");
    console.log("期望: 2025-06-06");
    console.log("实际: " + (result.expireDate || "null"));
}
